from model_registry.model_data import BerryModelData
from model_registry.model_feature import ModelFeatures

# from personal/bolian/start-bus-o3.sh
O3_0402_410 = BerryModelData(
    name="o3_0402_410",
    external_name="o3",
    snapshot_path="az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    extra_args=[
        "twppo.scallion.fp4moe8kv.sampling",
    ],
    extra_kwargs={
        "warmup_forward": False,
        "warmup_backward": False,
        "warmup_loss": False,
        "tensorcache_v2_limit_to_label": False,
        "enable_tensorcache_v2": True,
        "tensorcache_v2_indirect_load_threshold": 0,
        "ixf_batcher_type": "berry",
        "ixf_cpu_kv_cache_ratio": 0.0,
        "raise_on_load_for_missing_tensor": False,
        "allow_embedding_prefix_loading": True,
        "ixf_use_applied_comms": True,
        "twapi_use_per_pd_redis": True,
        "ixf_batch_send_mode": "redis_to_rank_zero_per_pd",
        "ixf_batcher_numa_bind": True,
        "ixf_batch_runner_numa_bind": True,
        "ixf_enable_presampling_mask_args_cache": True,
        "ixf_enable_batcher_full_pd_ahead_schedule": True,
        "image_encoder_type": "emb",
        "include_audio_embeddings": False,
        "ixf_max_cache_list_length": 4096,
        "ixf_kv_block_unit_size": 2048,
        "do_multimodal_projections_in_ixf_batch_runner": True,
    },
)


# from project/omom_msft/omom_msft/data/models_config.json
O3_MINI = BerryModelData(
    name="o3_mini",
    external_name="o3-mini",
    snapshot_path="az://orngcresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted",
    renderer_name="harmony_v4.0.15_berry_v3_16k_orion_text",
    extra_args=[],
    extra_kwargs={
        "raise_on_load_for_missing_tensor": False,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "enable_tensorcache_v2": False,
    },
)


# from project/omom_msft/omom_msft/data/models_config.json
O3_D64_STEP860 = BerryModelData(
    name="o3_d64_step860",
    external_name="o3",
    config_name="falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal",
    snapshot_path="az://orngcresco/models/snapshots/o3-d64-step860-20250324-retransferred-20250401-decrypted",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    extra_args=[],
    extra_kwargs={
        "load_inference_snapshots_with_tcv2": False,
        "raise_on_load_for_missing_tensor": False,
        "ixf_sampling_extension_gpu_to_cpu_async": False,
        "twapi_cpu_comm_backend": "gloo",
        "ixf_batcher_response_loop_timeout": 600,
        "allow_embedding_prefix_loading": True,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "decoupled_attention_token_mapping": "{None:0,(0,200166):0}",
        "n_op_shards": 8,
    },
    features=ModelFeatures(
        is_multimodal=True,
        encoder_decoder_snapshots='{"clip":"az://orngcresco/models/snapshots/mm/oaiomni/rcall/crow-export/scallion2b/omni/model_final_ts.pt"}',
    ),
)
