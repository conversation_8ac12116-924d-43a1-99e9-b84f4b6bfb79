# mopub - Model Publishing Tool

mopub is a command-line tool that automates the model checkpoint publishing process, enabling a streamlined one-command workflow for publishing model checkpoints to Nimbus.

## Overview

The mopub tool simplifies the complex process of converting model checkpoints from TensorCache (TC) format to Inference Exchange Format (IXF) and publishing them through the proper channels. It handles the entire pipeline from checkpoint conversion to Pull Request creation, making model deployment accessible through a single command.

### Usage

Execute the publishing command with your checkpoint path and desired export directory:

```bash
./run.sh <checkpoint_path> <export_directory>
```

**Parameters:**

- `checkpoint_path`: Azure blob storage path to your model checkpoint (must start with `az://`)
- `export_directory`: Name of the directory where the converted model will be stored

**Example:**

```bash
./run.sh az://oaici/torchflow/snapshots/my-model-checkpoint my-model-export-v1
```

### Workflow Steps

The mopub workflow consists of the following automated steps:

#### 1. Model Conversion

- Creates a Ray devbox cluster for processing
- Converts model weights from TC to IXF format
- Generates manifest files for deployment

#### 2. Notification

- Sends a notification to the "Model Publish Notification Only" Teams channel
- Includes the snapshot path and next steps command
- Mentions relevant team members

#### 3. Pull Request Creation

After receiving the Teams notification, execute the provided command:

```bash
~/code/glass/personal/lixiaoli/mopub/publish.sh <snapshot_name> <openai_user> <snapshot_file>
```

This command will:

- Create a new branch in the `oai-engine-configs` repository
- Generate a model ledger JSON file
- Create a Pull Request for review

#### 4. Nimbus Deployment

Once the PR is merged, it automatically triggers a pipeline that onboards the model to Nimbus.

### Cluster Configuration

The tool uses the following default cluster settings:

- **Cluster**: `prod-southcentralus-hpe-5`
- **Priority Class**: `team-critical`
- **GPUs**: 8 per job
- **Pods**: 1

## File Structure

```
mopub/
├── run.sh                    # Main entry point
├── convert.sh               # Conversion orchestration script
├── prepare_snapshot.py      # Core conversion logic
├── export_inference.py      # Model export functionality
├── notify.py               # Teams notification handler
├── webhook.py              # Teams webhook implementation
├── publish.sh              # PR creation script
└── tests/                  # Test files
    └── test_caas.py
```

## Troubleshooting

### Conversion Failures

- Check the `prepare_snapshot.log` file for detailed error messages
- Verify your checkpoint path is correct and accessible

### Log Files

- `mopub.log`: Main execution log
- `prepare_snapshot.log`: Detailed conversion process log

## Contributing

When contributing to mopub:

1. Test your changes thoroughly with non-production models
2. Update documentation for any new features
3. Follow existing code style and patterns
4. Add appropriate error handling and logging

## License

This tool is for internal use within the organization. Follow the company's policies regarding code usage and distribution.
