dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="mix16-arm2-$dt-peaval"
RENDERER_NAME="harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"

CMD=(
beam python --use-cwd -m qstar.run_eval nostrict
name=${EXPERIMENT_NAME}
root_config="mini.root.dev init_actors_rpc_timeout=600"
experiment_id=zhendongwang-mix16-arm-2-pdw2-ev3-mixed-itc-spi32-gpt5-mini-sft-2ep-tef03-5-tpm1-rm-lr1e-5-run3-ct8

peashooter.use_stale_kv_cache=False
peashooter.sampling_concurrency=1               # swang concurrent thread in one process
peashooter.num_sampling_processes=100            # swang concurrent process in one worker
#peashooter.tool_pool_config.num_tool_workers=64 # swang concurrent processes in tool pool
peashooter.timeout_seconds.stalled_datapoint=228000 # total time after this time, the datapoint is stall and reqeue, default 3600 * 4s
peashooter.timeout_seconds.lost_datapoint=300       # one rolling time after this time, the datapoint is lost and requeue, defualt 120s
peashooter.timeout_seconds.pause_sampling_after_controller_crash=3600
peashooter.sampling_use_ev3=True

# swang, not support
#timeout.evaluate=80000
#timeout.rollout=3600

# Set this if you want to eval all checkpoints at once in parallel (Not needed if your training run just started.)
max_workers_per_step=16 # swang nodes per step, cannot exceed total sample nodes
max_active_steps=4    # swang active steps

eval_settings.eval_every=20
eval_settings.min_step=80
# eval_settings.max_step=600
eval_settings.exit_on_no_new_checkpoint=True

# Inherit model params from training experiment!
auto_inherit_training_args=True

...dataset_container=orngscuscresco

# sbv data
# :deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_padawan
# eval_settings.evals.0.dataset.override_target_samples_per_instance=1
# eval_settings.evals.0.dataset.max_num_yields=100
# sbv data new
':deep_swe_eval_msft.swe_bench.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=768 override_target_samples_per_instance=1 max_num_yields=256)'

# msweb data
#:deep_swe_eval_msft.msweb.peaval.padawan.presets:ev_sm_bench_padawan
#eval_settings.evals.1.dataset.override_target_samples_per_instance=1
#eval_settings.evals.1.dataset.max_num_yields=100
# msweb data new
# ':deep_swe_eval_msft.msweb.peaval.padawan.presets:ev_sm_bench_juice_padawan(juice=768 override_target_samples_per_instance=1 max_num_yields=256)'

# SWE-Bench Hard data
# :prbot_msft.presets.swebench_hard:swebenchhard_12878_repair_msft_hq_padawan_testval
# :prbot_msft.presets.swebench_hard:o3_hpe_cotograder_bus

# swe-pr-if eval data
# :deep_swe_msft.rrb_if_ml_padawan_v2.presets:eval_padawan_v2_ml_sweif_prif 

metrics_collector=deep_swe_eval_msft.metrics.swe_metrics_collector:SWEMetricsCollector

#policy.n_ctx=524288
#defaults.n_ctx=524288
policy.n_ctx=262144
defaults.n_ctx=262144
defaults.sampler.max_num_yields=256
defaults.sampler.harmony_constrained_sampling=True
...harmony_renderer_name=${RENDERER_NAME}

# Set to True if you want to evaluate step0 (your prior)
eval_settings.eval_initial_policy=False

# msft special
load.restore_from_all_clusters=False

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-pevals
kafka_enable=False
)

GITHUB_UPLOAD_UNSAFE=1 "${CMD[@]}" 2>&1 | tee -a eval.log
