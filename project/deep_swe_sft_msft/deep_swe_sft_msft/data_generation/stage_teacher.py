"""
Stage Teacher Model for evaluating student completion of stages.
"""

import json
import re
import asyncio
from typing import List, Dict, Any
from dataclasses import dataclass

from chat.render.renderer_registry import get_renderer
from chat import chat, Role, Conversation
from message_completer.token_message_completer import TokenMessageCompleter
from message_completer.message_completer import Parse<PERSON>rrorMode
from bus_token_completer import BusTokenCompleter

from deep_swe_sft_msft.data_generation.constants import (
    BUS_USER,
    BUS_TOPIC,
)


# Simplified teacher evaluation prompt for stage-based approach
STAGE_TEACHER_PROMPT = """
You are evaluating whether a student has completed the current stage properly.

Current Stage: {stage_name}
Stage Criteria:
{criteria_list}

Student's work in this stage:
{conversation_messages}

Evaluate each criterion and respond with JSON:
{{
  "stage_complete": true/false,
  "criteria_met": [
    {{"criterion": "criteria1", "met": true/false, "explanation": "brief explanation"}},
    ...
  ],
  "overall_feedback": "brief feedback about what's missing or what was done well",
  "should_resample": true/false
}}

If should_resample is true, the student will try the stage again.
"""


@dataclass
class StageEvaluation:
    """Teacher's evaluation of a stage"""
    stage_complete: bool
    criteria_met: List[Dict[str, Any]]
    overall_feedback: str
    should_resample: bool
    stage_name: str


def get_render_constrained_extension(renderer_name: str):
    """Get the constrained extension for the renderer."""
    from qstar.common.tools import constraint_machine_spec
    from qstar.common.tools import renderer_worker
    
    harmony_stop_tokens_utils = renderer_worker.HarmonyStopTokensUtils(
        harmony_renderer_name=renderer_name,
        enable_special_stop_token=False,
    )
    
    extension = constraint_machine_spec.constraint_machine_extension(
        harmony_renderer_name=renderer_name,
        encoding_name='orion_200k',
        final_token_sequences=harmony_stop_tokens_utils.constraint_machine_final_token_sequences(),
        json_eot_tokens=harmony_stop_tokens_utils.constrained_sampling_json_eot_tokens(),
        tools=('functions.str_replace_editor', 'functions.report_progress', 'functions.bash', 'functions.write_bash', 'functions.read_bash', 'functions.stop_bash'),
        channels=("analysis", "final"),
        response_format_names=(),
        enable_json_constraining=False,
        none_channel_allowed=False,
    )
    return extension


def truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    """Truncate a string to a specified token limit"""
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"


class StageTeacherModel:
    """Simplified teacher model for stage-based evaluation"""
    
    def __init__(self, renderer_name: str, retries: int = 3, reward_multiplier: int = 32):
        self.renderer_name = renderer_name
        self.renderer = get_renderer(renderer_name)
        self.retries = retries
        self.reward_multiplier = reward_multiplier

        # Teacher model configuration
        bus_tc_config = BusTokenCompleter.Config(
            topic_mode_or_user=BUS_USER,
            topic_or_snapshot=BUS_TOPIC,
        )
        extension = get_render_constrained_extension(renderer_name)
        
        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=bus_tc_config,
            completion_params={"temperature": 1.0, "extensions": [extension]},
            renderer=self.renderer,
        )
        
        self.message_completer = message_completer_config.build()
    
    async def evaluate_stage(self, stage_name: str, stage_criteria: List[str], conversation: Conversation) -> StageEvaluation:
        """Evaluate if the student has completed the current stage properly"""
        
        # Format conversation for evaluation
        conversation_text = self._format_conversation_for_evaluation(conversation)
        
        # Format criteria as numbered list
        criteria_list = "\n".join([f"{i+1}. {criterion}" for i, criterion in enumerate(stage_criteria)])
        
        # Create evaluation prompt
        evaluation_prompt = STAGE_TEACHER_PROMPT.format(
            stage_name=stage_name,
            criteria_list=criteria_list,
            conversation_messages=conversation_text
        )
        
        print(f"Evaluating stage: {stage_name}")
        
        # Create teacher conversation for evaluation
        teacher_convo = chat.Conversation(
            messages=[
                chat.Message.system(
                    "You are an expert teacher evaluating student software engineering work. Evaluate stage completion strictly based on the provided criteria.",
                    metadata=chat.SystemContentMetadata(reward_multiplier=self.reward_multiplier)
                ),
                chat.Message.user(evaluation_prompt),
            ]
        )
        
        # Get teacher evaluation
        for attempt in range(self.retries):
            try:
                completion = await self.message_completer.async_completion(
                    conversations=[teacher_convo], n=1, seed=0, end_header=False,
                )
                choice = completion.choices[0]
                messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
                
                # Parse the teacher's response
                response_text = str(messages[-1].content)
                print(f"Teacher response: {response_text[:200]}...")
                
                evaluation = self._parse_stage_evaluation(response_text, stage_name, stage_criteria)
                return evaluation
                
            except Exception as e:
                if attempt < self.retries - 1:
                    print(f"Teacher evaluation attempt {attempt + 1} failed: {e}")
                    await asyncio.sleep(1)
                    continue
                else:
                    # Return failed evaluation if all attempts fail
                    return StageEvaluation(
                        stage_complete=False,
                        criteria_met=[{"criterion": c, "met": False, "explanation": f"Evaluation failed: {e}"} for c in stage_criteria],
                        overall_feedback=f"Teacher evaluation failed after {self.retries} attempts: {e}",
                        should_resample=True,
                        stage_name=stage_name
                    )
    
    def _format_conversation_for_evaluation(self, conversation: Conversation, token_limit: int = 512, tool_truncation_rate: float = 0.5) -> str:
        """Format conversation for teacher evaluation"""
        formatted_messages = []
        for msg in conversation.messages:
            role = msg.author.role

            if role == Role.SYSTEM:
                continue # skip system messages for now
                # content = system_content_render(msg.content)
            else:
                content = str(msg.content)
                if role != Role.TOOL:
                    cur_token_limit = token_limit
                else:
                    cur_token_limit = int(token_limit * tool_truncation_rate)
                content = truncate_string(self.renderer, content, token_limit=cur_token_limit)
                
            formatted_messages.append(f"<author:{msg.author.role}> -> <recipient:{msg.recipient}>:  {content}\n")

        return "\n".join(formatted_messages)
    
    def _parse_stage_evaluation(self, response_text: str, stage_name: str, stage_criteria: List[str]) -> StageEvaluation:
        """Parse teacher's stage evaluation response"""
        # Try to extract JSON from the response
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            try:
                response_data = json.loads(json_match.group())
                
                return StageEvaluation(
                    stage_complete=response_data.get("stage_complete", False),
                    criteria_met=response_data.get("criteria_met", []),
                    overall_feedback=response_data.get("overall_feedback", ""),
                    should_resample=response_data["should_resample"], # should have this key otherwise it is an error
                    stage_name=stage_name
                )
            except json.JSONDecodeError as e:
                raise ValueError(f"Failed to parse JSON from teacher response: {e}")
        else:
            raise ValueError(f"Could not find JSON in teacher response: {response_text[:200]}...")
