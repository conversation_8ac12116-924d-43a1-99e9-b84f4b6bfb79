"""
Corrector Model for fixing errors in student messages and tool calls.
"""

import json
import re
import asyncio
import copy
from typing import List, Dict, Any, Tu<PERSON>, Optional
from dataclasses import dataclass

from chat.render.renderer_registry import get_renderer
from chat import chat, Role, Conversation
from chat.chat import Text
from chat.tools import take_one_step_with_tools
from message_completer.token_message_completer import TokenMessageCompleter
from message_completer.message_completer import ParseErrorMode
from bus_token_completer import BusTokenCompleter

from deep_swe_sft_msft.data_generation.constants import (
    BUS_USER,
    BUS_TOPIC,
    PADAWAN_TOOL_ERROR_MSGS
)


# Corrector prompt for fixing errors in student messages
CORRECTOR_PROMPT = """
You are an expert corrector that fixes errors in student software engineering work.

The student made a tool call that resulted in an error. Your job is to analyze the error and decide whether to fix it or skip it.

Context of the conversation (last 10 messages):
{context}

Student's original tool call content:
{student_content}

Tool error output:
{tool_error_output}

You must respond with a JSON object containing two fields:
- "action": either "skip" or "replace"
- "content": the content to use

Decision criteria:
- Use "skip" if:
  * The error is acceptable/expected behavior
  * The tool call is exploratory and the error provides useful information
  * The error doesn't significantly impact the workflow
- Use "replace" if:
  * The error can be fixed with a corrected tool call
  * The tool call has syntax errors or invalid parameters
  * The error prevents meaningful progress

For "skip" action: set "content" to the original student content
For "replace" action: set "content" to the corrected tool call JSON string

Respond with JSON only:
{{
  "action": "skip" or "replace",
  "content": "the tool call content to use"
}}

Guidelines:
- If the tool call error is from a meaningful tool call, focus on solving the tool call error; ignore the context.
- If the tool call error is from a non-meaningful tool call, like calling `bash` without any command, or calling `view` without any path, you can reference the context to provide a more meaningful correction.
- Return ONLY the JSON response, no additional text or explanation.
"""


@dataclass
class CorrectionResult:
    """Result of a correction attempt"""
    success: bool
    corrected_student_messages: List = None
    corrected_tool_messages: List = None
    error_message: str = ""


def get_render_constrained_extension(renderer_name: str):
    """Get the constrained extension for the renderer."""
    from qstar.common.tools import constraint_machine_spec
    from qstar.common.tools import renderer_worker
    
    harmony_stop_tokens_utils = renderer_worker.HarmonyStopTokensUtils(
        harmony_renderer_name=renderer_name,
        enable_special_stop_token=False,
    )
    
    extension = constraint_machine_spec.constraint_machine_extension(
        harmony_renderer_name=renderer_name,
        encoding_name='orion_200k',
        final_token_sequences=harmony_stop_tokens_utils.constraint_machine_final_token_sequences(),
        json_eot_tokens=harmony_stop_tokens_utils.constrained_sampling_json_eot_tokens(),
        tools=('functions.str_replace_editor', 'functions.report_progress', 'functions.bash', 'functions.write_bash', 'functions.read_bash', 'functions.stop_bash'),
        channels=("analysis", "final"),
        response_format_names=(),
        enable_json_constraining=False,
        none_channel_allowed=False,
    )
    return extension


def has_tool_error(tool_message):
    """Check if a tool message contains error messages from PADAWAN_TOOL_ERROR_MSGS"""
    content = str(tool_message.content)
    return any(error_msg in content for error_msg in PADAWAN_TOOL_ERROR_MSGS)


class Corrector:
    """Corrector model for fixing errors in student messages and tool calls"""
    
    def __init__(self, renderer_name: str, retries: int = 3, reward_multiplier: int = 32):
        self.renderer_name = renderer_name
        self.renderer = get_renderer(renderer_name)
        self.retries = retries
        self.reward_multiplier = reward_multiplier

        # Corrector model configuration
        bus_tc_config = BusTokenCompleter.Config(
            topic_mode_or_user=BUS_USER,
            topic_or_snapshot=BUS_TOPIC,
        )
        extension = get_render_constrained_extension(renderer_name)
        
        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=bus_tc_config,
            completion_params={"temperature": 1.0, "extensions": [extension]},
            renderer=self.renderer,
        )
        
        self.message_completer = message_completer_config.build()
    
    async def correct_step_if_needed(
        self, 
        step_student_messages: List, 
        step_tool_messages: List, 
        current_convo: Conversation,
        toolkit,
        valid_recipients: set
    ) -> CorrectionResult:
        """
        Correct the student messages based on tool errors.
        
        Args:
            step_student_messages: The student messages from this step
            step_tool_messages: The tool messages from this step (assumed to contain errors)
            current_convo: The current conversation state before this step
            toolkit: The toolkit for re-executing tools
            valid_recipients: Set of valid recipients for messages
            
        Returns:
            CorrectionResult with corrected messages
        """
        
        print(f"Attempting correction for {len(step_tool_messages)} tool error(s)...")
        
        # Try to correct the student messages
        try:
            # Use only the last 10 messages as context
            last_10_messages = current_convo.messages[-10:] if len(current_convo.messages) > 10 else current_convo.messages
            limited_convo = Conversation(messages=last_10_messages)
            current_context = self._format_conversation_for_evaluation(limited_convo)
            corrected_student_messages = await self._correct_student_messages(
                step_student_messages, step_tool_messages, context=current_context
            )
            
            if not corrected_student_messages:
                return CorrectionResult(
                    success=False,
                    error_message="Failed to generate corrected student messages"
                )
            
            # Re-execute tools with corrected messages
            corrected_tool_messages = await self._re_execute_tools(
                corrected_student_messages, current_convo, toolkit, valid_recipients
            )
            
            return CorrectionResult(
                success=True,
                corrected_student_messages=corrected_student_messages,
                corrected_tool_messages=corrected_tool_messages
            )
            
        except Exception as e:
            return CorrectionResult(
                success=False,
                error_message=f"Correction failed: {str(e)}"
            )
    
    async def _correct_student_messages(self, student_messages: List, error_tool_messages: List, context: str = "") -> List:
        """Correct student messages based on tool errors"""
        
        # Focus on the last student message that likely caused the error
        if not student_messages:
            return []
        
        last_student_message = student_messages[-1]
        
        # Combine all error outputs for context
        error_outputs = []
        for error_msg in error_tool_messages:
            error_outputs.append(str(error_msg.content))
        
        combined_error = "\n".join(error_outputs)
        
        # Extract the content from the student message
        try:
            student_content = str(last_student_message.content)
        except Exception as e:
            print(f"Failed to extract student content: {e}")
            return []
        
        # Create correction prompt
        correction_prompt = CORRECTOR_PROMPT.format(
            context=context,
            student_content=student_content,
            tool_error_output=combined_error
        )
        
        print(f"Requesting correction for student message...")
        
        # Create corrector conversation
        corrector_convo = chat.Conversation(
            messages=[
                chat.Message.system(
                    "You are an expert corrector that analyzes software engineering tool call errors and decides whether to fix or skip them. Respond with a JSON object containing 'action' (skip/replace) and 'content' fields only.",
                    metadata=chat.SystemContentMetadata(reward_multiplier=self.reward_multiplier)
                ),
                chat.Message.user(correction_prompt),
            ]
        )
        # print(corrector_convo) # for debugging
        
        # Get corrector response
        for attempt in range(self.retries):
            try:
                completion = await self.message_completer.async_completion(
                    conversations=[corrector_convo], n=1, seed=0, end_header=False,
                )
                choice = completion.choices[0]
                response_messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
                
                # Parse the corrected message
                response_text = str(response_messages[-1].content)
                print(f"Corrector response: {response_text[:200]}...")
                
                corrected_message = self._parse_corrected_message(response_text, last_student_message)
                
                if corrected_message:
                    # Return all original messages except the last one, plus the corrected last message
                    return student_messages[:-1] + [corrected_message]
                else:
                    print(f"Failed to parse corrected message, attempt {attempt + 1}")
                    
            except Exception as e:
                print(f"Corrector attempt {attempt + 1} failed: {e}")
                if attempt < self.retries - 1:
                    await asyncio.sleep(1)
                    continue
        
        print("All correction attempts failed")
        return []
    
    async def _re_execute_tools(
        self, 
        corrected_student_messages: List, 
        current_convo: Conversation,
        toolkit,
        valid_recipients: set
    ) -> List:
        """Re-execute tools with corrected student messages"""
        
        if not corrected_student_messages:
            return []
        
        # Create conversation with corrected student messages
        step_convo = current_convo.with_suffix(*corrected_student_messages)
        last_message = corrected_student_messages[-1]
        
        # Check if tools should be executed
        should_execute_tools = not (
            last_message.end_turn
            or (last_message.recipient not in valid_recipients and last_message.recipient.split(".")[0] not in valid_recipients)
        )
        
        corrected_tool_messages = []
        if should_execute_tools:
            try:
                # Execute tools with corrected message
                async for tool_message in take_one_step_with_tools(
                    prefix_convo=step_convo.prefix(-1),
                    message=last_message,
                    toolkit=toolkit,
                ):
                    corrected_tool_messages.append(tool_message)
                    # Update the step conversation for subsequent tool calls
                    if step_convo.messages[-1].id == tool_message.id:
                        step_convo.messages[-1] = tool_message
                    else:
                        step_convo = step_convo.with_suffix(tool_message)
                        
                print(f"Re-executed tools, got {len(corrected_tool_messages)} tool messages")
                
            except Exception as e:
                print(f"Tool re-execution failed: {e}")
                return []
        
        return corrected_tool_messages
    
    def _parse_corrected_message(self, response_text: str, original_message) -> Optional[Any]:
        """Parse the corrected message from corrector response"""
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            try:
                # Create a new message object with corrected content
                corrected_message = copy.deepcopy(original_message)
                
                # The response should be a JSON with action and content fields
                response_text = response_text.strip()
                
                # Parse the JSON response
                try:
                    response_json = json.loads(json_match.group())
                except json.JSONDecodeError:
                    print(f"Response is not valid JSON: {response_text[:200]}...")
                    raise ValueError("Response is not valid JSON")
                
                # Validate required fields
                if "action" not in response_json or "content" not in response_json:
                    print(f"Response missing required fields: {response_json}")
                    raise ValueError("Response missing 'action' or 'content' field")
                
                action = response_json["action"]
                content = response_json["content"]
                
                # Validate action value
                if action not in ["skip", "replace"]:
                    print(f"Invalid action value: {action}")
                    raise ValueError(f"Invalid action value: {action}")
                
                # For skip action, we could return None to indicate no change needed
                # But for consistency, we'll return the original message with original content
                if action == "skip":
                    print("Corrector decided to skip - using original content")
                    return original_message
                
                # For replace action, validate that content is valid JSON (tool call)
                if action == "replace":
                    try:
                        json.loads(content)
                    except json.JSONDecodeError:
                        print(f"Replacement content is not valid JSON: {content[:200]}...")
                        raise ValueError("Replacement content is not valid JSON")
                    
                    print("Corrector decided to replace - using corrected content")
                    corrected_message.content = Text.from_string(content)
                    return corrected_message
                    
            except Exception as e:
                print(f"Error creating corrected message: {e}")
                return None
        else:
            raise ValueError(f"Could not find JSON in corrector response: {response_text[:200]}...")

    def _format_conversation_for_evaluation(self, conversation: Conversation, token_limit: int = 256, tool_truncation_rate: float = 0.5) -> str:
        """Format conversation for teacher evaluation"""
        formatted_messages = []
        for msg in conversation.messages:
            role = msg.author.role

            if role == Role.SYSTEM:
                continue # skip system messages for now
                # content = system_content_render(msg.content)
            else:
                content = str(msg.content)
                if role != Role.TOOL:
                    cur_token_limit = token_limit
                else:
                    cur_token_limit = int(token_limit * tool_truncation_rate)
                content = truncate_string(self.renderer, content, token_limit=cur_token_limit)
                
            formatted_messages.append(f"<author:{msg.author.role}> -> <recipient:{msg.recipient}>:  {content}\n")

        return "\n".join(formatted_messages)
    
    
def truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    """Truncate a string to a specified token limit"""
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"