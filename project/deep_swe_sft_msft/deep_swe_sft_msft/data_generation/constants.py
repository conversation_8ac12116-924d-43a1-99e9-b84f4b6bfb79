# CAAS_ENDPOINT = "https://eastus2.caas.azure.com"
CAAS_ENDPOINT = "https://southcentralus.caas.azure.com"

BUS_USER = "swe-sft-data"
BUS_TOPIC = "az://orngscuscresco/models/snapshots/zen-os4-nv4-cbv6-hh-rkld-4jul-orca-2-2025-07-10-00-04-decrypted"
STUDENT_RENDERER = "harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"
TEACHER_RENDERER = "harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"

PADAWAN_TOOL_ERROR_MSGS = [
    'Error parsing function call:',
    'Could not parse args as JSON:',
    'parameter is required',
    'Unrecognized function name', 'Invalid functions.', 'Wrong functions (with', 'Model cannot call'
]