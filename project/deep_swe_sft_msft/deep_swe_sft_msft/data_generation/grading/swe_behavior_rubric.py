"""
SWE Behavior Rubric - Defines criteria for evaluating software engineering conversation quality.

This rubric focuses on identifying good vs bad SWE behavior patterns in conversations,
particularly around tool usage, file exploration patterns, and problem-solving approach.
"""

# Main SWE Behavior Rubric
SWE_BEHAVIOR_RUBRIC = """
Evaluate this software engineering conversation based on the following BINARY criteria. 
Each criterion must be evaluated as PASS or FAIL. ALL criteria must pass for a good conversation.

CRITERIA (ALL MUST PASS):

1. **Tool Call Reliability (criterion1)**
   - No tool call errors, all function calls are properly formatted and executed successfully
   - Any tool call syntax errors, parameter errors, JSON parsing errors are considered a FAIL
   - Execution failures are okay if they are handled properly

2. **File Exploration Efficiency (criterion2)**
   - Each file/region is read only when necessary, no redundant file viewing
   - Don't view the same file content or code regions multiple times unnecessarily

3. **Focused Problem Solving (criterion3)**
   - Maintains clear focus on the stated problem throughout the conversation
   - Never gets distracted and avoids unnecessary context switching

4. **Reflection (criterion4)**
   - Model should reflect on its errors quickly and adjust its approach
   - If it makes a mistake, it should acknowledge it and correct its course of action

SPECIFIC FAILURE INDICATORS:
- Tool call syntax errors or JSON parsing failures
- Reading the same file content more than twice
- Jumping between unrelated files without purpose
- Making changes without proper analysis
- Ignoring error messages or test failures
- Not following systematic problem-solving approach
"""