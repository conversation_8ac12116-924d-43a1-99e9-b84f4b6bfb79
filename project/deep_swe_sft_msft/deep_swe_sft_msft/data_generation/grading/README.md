# Conversation Grading System

This package provides a comprehensive grading system for evaluating software engineering conversation quality using **binary criteria evaluation**. It follows the `StageTeacherModel` pattern for consistency with the existing codebase.

## Overview

The grading system uses a **strict binary evaluation approach**:
- Each criterion is evaluated as **PASS** or **FAIL**
- **ALL criteria must pass** for a conversation to be considered "good"
- If **any criterion fails**, the conversation is automatically marked as "bad"
- This ensures only high-quality conversations that meet all SWE standards are accepted

## Key Components

1. **ConversationGrader** - Main grader class that evaluates conversations based on rubrics
2. **SWE Behavior Rubrics** - Predefined binary rubrics for different evaluation scenarios  
3. **Utility Functions** - Simple interfaces for common grading tasks

## Binary Evaluation Logic

```python
# Conversation is good ONLY if ALL criteria pass
all_criteria_passed = True
for criterion, details in criterion_scores.items():
    if not details.get("passed", False):
        all_criteria_passed = False
        break

is_good_conversation = all_criteria_passed
```

## Quick Start

### Basic Usage

```python
from grading import grade_conversation, SWE_BEHAVIOR_RUBRIC
from chat import Conversation

# Simple True/False evaluation
is_good = await grade_conversation(
    conversation=my_conversation,
    rubric_prompt=SWE_BEHAVIOR_RUBRIC,
    rubric_name="swe_behavior"
)
print(f"Conversation is {'good' if is_good else 'bad'}")
```

### Detailed Evaluation

```python
from grading import evaluate_conversation_detailed, SWE_BEHAVIOR_RUBRIC

evaluation = await evaluate_conversation_detailed(
    conversation=my_conversation,
    rubric_prompt=SWE_BEHAVIOR_RUBRIC,
    rubric_name="swe_behavior"
)

print(f"Overall Score: {evaluation.overall_score:.2f} (fraction of criteria passed)")
print(f"Is Good: {evaluation.is_good_conversation}")
print(f"Feedback: {evaluation.overall_feedback}")

for criterion, details in evaluation.criterion_scores.items():
    passed = details.get('passed', False)
    status = "✅ PASS" if passed else "❌ FAIL"
    print(f"{criterion}: {status} - {details.get('explanation', '')}")
```

### Using the ConversationGrader Class

```python
from grading import ConversationGrader, SWE_BEHAVIOR_RUBRIC

grader = ConversationGrader()
evaluation = await grader.evaluate_conversation(
    conversation=my_conversation,
    rubric_prompt=SWE_BEHAVIOR_RUBRIC,
    rubric_name="swe_behavior"
)
```

## Available Rubrics

### 1. SWE_BEHAVIOR_RUBRIC (Recommended)
The main rubric for evaluating software engineering conversations using **binary criteria**:

- **Tool Call Reliability** - PASS: No tool call errors; FAIL: Any syntax/parsing errors
- **File Exploration Efficiency** - PASS: No redundant file viewing; FAIL: Same content viewed multiple times
- **Focused Problem Solving** - PASS: Clear focus throughout; FAIL: Context switching or lack of direction
- **Logical File Navigation** - PASS: Systematic exploration; FAIL: Random jumping between files
- **Code Quality** - PASS: Best practices and testing; FAIL: Poor quality or inappropriate changes
- **Problem Resolution** - PASS: Successfully resolves issue; FAIL: Fails to understand or fix problem

**Evaluation Rule**: Conversation is "good" ONLY if ALL 6 criteria PASS.

### 2. SIMPLE_SWE_RUBRIC
A simplified version with three binary criteria:
- No tool errors
- Efficient file reading  
- Logical navigation

**Evaluation Rule**: Conversation is "good" ONLY if ALL 3 criteria PASS.

### 3. SWE_ANTIPATTERN_RUBRIC
Focused on detecting anti-patterns (all must be absent):
- Tool call failures
- Redundant file operations
- Aimless navigation
- Incomplete problem understanding
- Poor error handling

**Evaluation Rule**: Conversation is "good" ONLY if ALL anti-patterns are ABSENT.

## Integration with Data Generation Pipeline

### Filtering Generated Conversations

```python
from grading import ConversationGrader, SWE_BEHAVIOR_RUBRIC

async def filter_high_quality_conversations(conversations, min_score=0.7):
    grader = ConversationGrader()
    good_conversations = []
    
    for conversation in conversations:
        evaluation = await grader.evaluate_conversation(
            conversation=conversation,
            rubric_prompt=SWE_BEHAVIOR_RUBRIC,
            rubric_name="swe_behavior"
        )
        
        if evaluation.is_good_conversation and evaluation.overall_score >= min_score:
            good_conversations.append({
                'conversation': conversation,
                'grading_metadata': evaluation
            })
    
    return good_conversations
```

### Batch Processing

```python
from grading import ConversationGrader, SWE_BEHAVIOR_RUBRIC

async def grade_conversation_batch(conversations):
    grader = ConversationGrader()
    results = []
    
    for conversation in conversations:
        evaluation = await grader.evaluate_conversation(
            conversation=conversation,
            rubric_prompt=SWE_BEHAVIOR_RUBRIC,
            rubric_name="swe_behavior"
        )
        results.append(evaluation)
    
    # Calculate statistics
    good_count = sum(1 for r in results if r.is_good_conversation)
    avg_score = sum(r.overall_score for r in results) / len(results)
    
    print(f"Good conversations: {good_count}/{len(conversations)} ({good_count/len(conversations)*100:.1f}%)")
    print(f"Average score: {avg_score:.2f}")
    
    return results
```

## Criteria Details

### Tool Call Reliability
- **Good**: All function calls properly formatted and executed
- **Bad**: Frequent JSON parsing errors, syntax errors, parameter mismatches

### File Exploration Efficiency  
- **Good**: Each file/region read only when necessary
- **Bad**: Repeatedly viewing the same files or code regions

### Logical File Navigation
- **Good**: Systematic exploration, clear progression through codebase
- **Bad**: Random jumping between files, excessive back-and-forth

### Problem Focus
- **Good**: Maintains clear focus on the stated problem
- **Bad**: Frequent context switching, lacks direction

### Code Quality
- **Good**: Follows best practices, proper testing, clean implementation
- **Bad**: Poor coding standards, no testing, introduces new issues

### Problem Resolution
- **Good**: Successfully identifies and resolves the core issue
- **Bad**: Fails to understand or fix the problem

## Configuration

The grader uses the same configuration as `StageTeacherModel`:

```python
# Default configuration
grader = ConversationGrader(
    renderer_name=TEACHER_RENDERER,  # From constants.py
    retries=3,
    reward_multiplier=32
)
```

## Error Handling

The grader includes robust error handling:

- **Retries**: Automatic retry logic for transient failures
- **Graceful degradation**: Returns failed evaluation objects when grading fails
- **Detailed error messages**: Clear feedback when something goes wrong

## Custom Rubrics

You can create custom rubrics by following this format:

```python
CUSTOM_RUBRIC = """
Evaluate this conversation based on the following criteria:

CRITERIA:

1. **Custom Criterion (criterion1)**
   - PASS: Excellent performance on this aspect
   - FAIL: Poor performance on this aspect

2. **Another Criterion (criterion2)**
   - PASS: Meets requirements
   - FAIL: Does not meet requirements

Conversation is "good" if ALL criteria PASS.
"""

# Use with grader
evaluation = await grade_conversation(
    conversation=my_conversation,
    rubric_prompt=CUSTOM_RUBRIC,
    rubric_name="custom"
)
```

## Testing

Run the test script to verify functionality:

```bash
cd grading/
python test_grader.py
```

Note: Tests require proper infrastructure setup (BusTokenCompleter, etc.) to run.

## API Reference

### ConversationGrader

#### `__init__(renderer_name, retries=3, reward_multiplier=32)`
Initialize the grader with configuration options.

#### `async evaluate_conversation(conversation, rubric_prompt, rubric_name)`
Evaluate a conversation and return detailed results.

### Utility Functions

#### `async grade_conversation(conversation, rubric_prompt, rubric_name)`
Simple function that returns True/False for conversation quality.

#### `async evaluate_conversation_detailed(conversation, rubric_prompt, rubric_name)`
Returns full ConversationEvaluation object with detailed scores.

### ConversationEvaluation

Data class containing evaluation results:
- `is_good_conversation: bool`
- `overall_score: float` (0.0 to 1.0)
- `criterion_scores: Dict[str, Dict[str, Any]]`
- `overall_feedback: str`
- `rubric_name: str`

## Performance Considerations

- **Concurrency**: The grader is async-first and can handle concurrent evaluations
- **Token limits**: Conversations are automatically truncated to fit model context limits
- **Caching**: Consider implementing caching for repeated evaluations of the same conversations
- **Batch size**: For large-scale grading, process in batches to manage memory usage

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure all dependencies are installed and the module path is correct
2. **Authentication errors**: Verify BusTokenCompleter configuration
3. **Timeout errors**: Increase retry count or check network connectivity
4. **JSON parsing errors**: Usually indicates model output format issues

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```
