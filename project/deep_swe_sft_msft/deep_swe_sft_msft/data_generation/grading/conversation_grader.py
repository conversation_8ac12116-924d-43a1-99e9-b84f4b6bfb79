"""
Conversation Grader - Evaluates conversation quality based on rubrics.

This grader follows the StageTeacherModel pattern and can take in a rubric prompt 
and a whole conversation and output true or false to determine if it's a good conversation.
"""

import json
import re
import asyncio
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from chat.render.renderer_registry import get_renderer
from chat.render.common import system_content_render
from chat import chat, Conversation, Role, Message
from message_completer.message_completer import MessageCompleter, ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter
from bus_token_completer import BusTokenCompleter

from deep_swe_sft_msft.data_generation.constants import BUS_USER, BUS_TOPIC, TEACHER_RENDERER


@dataclass
class ConversationEvaluation:
    """Result of conversation evaluation"""
    is_good_conversation: bool
    overall_score: float  # 0.0 to 1.0
    criterion_scores: Dict[str, Dict[str, Any]]  # criterion_name -> {score, explanation}
    overall_feedback: str
    rubric_name: str


class ConversationGrader:
    """
    Grader that evaluates conversation quality based on provided rubrics.
    
    Follows the StageTeacherModel pattern for consistency with existing codebase.
    """
    
    def __init__(self, renderer_name: str = TEACHER_RENDERER, retries: int = 3, reward_multiplier: int = 32):
        self.renderer_name = renderer_name
        self.renderer = get_renderer(renderer_name)
        self.retries = retries
        self.reward_multiplier = reward_multiplier

        # Teacher model configuration (same as StageTeacherModel)
        bus_tc_config = BusTokenCompleter.Config(
            topic_mode_or_user=BUS_USER,
            topic_or_snapshot=BUS_TOPIC,
        )
        
        # Note: Extension setup may need to be added if required
        # extension = get_render_constrained_extension(renderer_name)
        
        message_completer_config = TokenMessageCompleter.Config(
            token_completer_config=bus_tc_config,
            completion_params={"temperature": 0.1},  # Lower temperature for more consistent grading
            renderer=self.renderer,
        )
        
        self.message_completer = message_completer_config.build()
    
    async def evaluate_conversation(self, conversation: Conversation, rubric_prompt: str, rubric_name: str = "custom") -> ConversationEvaluation:
        """
        Evaluate a conversation based on the provided rubric.
        
        Args:
            conversation: The conversation to evaluate
            rubric_prompt: The rubric criteria and instructions
            rubric_name: Name of the rubric being used
            
        Returns:
            ConversationEvaluation with detailed results
        """
        
        # Format conversation for evaluation
        conversation_text = self._format_conversation_for_evaluation(conversation)
        
        # Create evaluation prompt
        evaluation_prompt = self._create_evaluation_prompt(rubric_prompt, conversation_text)
        
        print(f"Evaluating conversation with rubric: {rubric_name}")
        
        # Create teacher conversation for evaluation
        teacher_convo = Conversation(
            messages=[
                Message.system(
                    "You are an expert evaluator of software engineering conversations. "
                    "Evaluate conversations strictly based on the provided rubric and return precise JSON results.",
                    metadata=chat.SystemContentMetadata(reward_multiplier=self.reward_multiplier)
                ),
                Message.user(evaluation_prompt),
            ]
        )
        
        # Get teacher evaluation with retries
        for attempt in range(self.retries):
            try:
                completion = await self.message_completer.async_completion(
                    conversations=[teacher_convo], n=1, seed=0, end_header=False,
                )
                choice = completion.choices[0]
                messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
                
                # Parse the teacher's response
                response_text = str(messages[-1].content)
                print(f"Grader response: {response_text[:200]}...")
                
                evaluation = self._parse_evaluation_response(response_text, rubric_name)
                return evaluation
                
            except Exception as e:
                if attempt < self.retries - 1:
                    print(f"Conversation evaluation attempt {attempt + 1} failed: {e}")
                    await asyncio.sleep(1)
                    continue
                else:
                    # Return failed evaluation if all attempts fail
                    return ConversationEvaluation(
                        is_good_conversation=False,
                        overall_score=0.0,
                        criterion_scores={},
                        overall_feedback=f"Evaluation failed after {self.retries} attempts: {e}",
                        rubric_name=rubric_name
                    )
    
    def _format_conversation_for_evaluation(self, conversation: Conversation, token_limit: int = 512, tool_truncation_rate: float = 0.3) -> str:
        """Format conversation for evaluation (similar to StageTeacherModel)"""
        formatted_messages = []
        
        for msg in conversation.messages:
            role = msg.author.role

            if role == Role.SYSTEM:
                continue  # Skip system messages for evaluation
            else:
                content = str(msg.content)
                if role != Role.TOOL:
                    cur_token_limit = token_limit
                else:
                    cur_token_limit = int(token_limit * tool_truncation_rate)
                content = self._truncate_string(content, token_limit=cur_token_limit)
                
            formatted_messages.append(f"<{role.name}> {content}\n")

        return "\n".join(formatted_messages)
    
    def _truncate_string(self, string: str, token_limit: int = 512, truncate_behavior: str = "middle") -> str:
        """Truncate a string to a specified token limit"""
        toks = self.renderer.encode(string)
        if len(toks) < token_limit:
            return string

        if truncate_behavior == "middle":
            return (
                self.renderer.decode(toks[: token_limit // 2])
                + "...(truncated)..."
                + self.renderer.decode(toks[-token_limit // 2 :])
            )

        return self.renderer.decode(toks[:token_limit]) + "...(truncated)"
    
    def _create_evaluation_prompt(self, rubric_prompt: str, conversation_text: str) -> str:
        """Create the evaluation prompt for the teacher model"""
        return f"""
Evaluate the following software engineering conversation based on the rubric provided.

RUBRIC:
{rubric_prompt}

CONVERSATION TO EVALUATE:
{conversation_text}

Evaluate the conversation and respond with JSON in this exact format:
{{
  "is_good_conversation": true/false,
  "criterion_scores": {{
    "criterion1": {{"passed": true/false, "explanation": "Explanation for this criterion"}},
    "criterion2": {{"passed": true/false, "explanation": "Explanation for this criterion"}},
    "criterion3": {{"passed": true/false, "explanation": "Explanation for this criterion"}}
  }},
  "overall_feedback": "Brief overall assessment of the conversation quality"
}}

IMPORTANT: 
- Each criterion should be evaluated as PASS/FAIL (true/false)
- Use criterion1, criterion2, criterion3, etc. as keys (match the number of criteria in the rubric)
- The conversation is considered "good" ONLY if ALL criteria pass (all "passed": true)
- If ANY criterion fails, the conversation is automatically considered bad
- Provide clear explanations for why each criterion passed or failed
"""
    
    def _parse_evaluation_response(self, response_text: str, rubric_name: str) -> ConversationEvaluation:
        """Parse the teacher's evaluation response"""
        # Try to extract JSON from the response
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            try:
                response_data = json.loads(json_match.group())
                
                # Extract criterion results and check if all passed
                criterion_scores = response_data.get("criterion_scores", {})
                all_criteria_passed = True
                passed_count = 0
                total_count = len(criterion_scores)
                
                # Check each criterion - all must pass for good conversation
                for criterion_name, details in criterion_scores.items():
                    if details.get("passed", False):
                        passed_count += 1
                    else:
                        all_criteria_passed = False
                
                # Calculate overall score as percentage of criteria passed
                overall_score = passed_count / total_count if total_count > 0 else 0.0
                
                # Override is_good_conversation based on all criteria passing
                is_good = all_criteria_passed and (passed_count == total_count)
                
                return ConversationEvaluation(
                    is_good_conversation=is_good,
                    overall_score=overall_score,
                    criterion_scores=criterion_scores,
                    overall_feedback=response_data.get("overall_feedback", ""),
                    rubric_name=rubric_name
                )
            except json.JSONDecodeError as e:
                raise ValueError(f"Failed to parse JSON from grader response: {e}")
        else:
            raise ValueError(f"Could not find JSON in grader response: {response_text[:800]}...")


# Simple function interface for easy use
async def grade_conversation(conversation: Conversation, rubric_prompt: str, rubric_name: str = "custom") -> bool:
    """
    Simple function to grade a conversation and return True/False.
    
    Args:
        conversation: The conversation to evaluate
        rubric_prompt: The rubric criteria and instructions
        rubric_name: Name of the rubric being used
        
    Returns:
        bool: True if good conversation, False otherwise
    """
    grader = ConversationGrader()
    evaluation = await grader.evaluate_conversation(conversation, rubric_prompt, rubric_name)
    return evaluation.is_good_conversation


# More detailed function interface for comprehensive evaluation
async def evaluate_conversation_detailed(conversation: Conversation, rubric_prompt: str, rubric_name: str = "custom") -> ConversationEvaluation:
    """
    Detailed function to grade a conversation and return full evaluation.
    
    Args:
        conversation: The conversation to evaluate
        rubric_prompt: The rubric criteria and instructions
        rubric_name: Name of the rubric being used
        
    Returns:
        ConversationEvaluation: Detailed evaluation results
    """
    grader = ConversationGrader()
    return await grader.evaluate_conversation(conversation, rubric_prompt, rubric_name)
