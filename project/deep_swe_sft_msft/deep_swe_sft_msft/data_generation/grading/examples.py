"""
Example usage of the ConversationGrader with SWE behavior rubrics.

This file demonstrates how to use the new grading system to evaluate
software engineering conversations for quality assessment.
"""

import asyncio
from typing import List

from chat import Conversation, Message, Role
from .conversation_grader import ConversationGrader, grade_conversation, evaluate_conversation_detailed
from .swe_behavior_rubric import (
    SWE_BEHAVIOR_RUBRIC,
    SIMPLE_SWE_RUBRIC,
    SWE_ANTIPATTERN_RUBRIC,
    COMPREHENSIVE_SWE_RUBRIC
)


async def example_basic_grading():
    """Example of basic conversation grading"""
    
    # Create a sample conversation (in practice, this would come from your data)
    sample_conversation = Conversation(messages=[
        Message.system("You are a helpful software engineering assistant."),
        Message.user("I need to fix a bug in my Python code where the function returns None instead of a list."),
        Message.assistant("I'll help you fix that bug. Let me first examine your code to understand the issue."),
        # ... more messages would be here in a real conversation
    ])
    
    # Grade using the main SWE behavior rubric
    is_good = await grade_conversation(
        conversation=sample_conversation,
        rubric_prompt=SWE_BEHAVIOR_RUBRIC,
        rubric_name="swe_behavior"
    )
    
    print(f"Conversation quality: {'Good' if is_good else 'Bad'}")
    return is_good


async def example_detailed_grading():
    """Example of detailed conversation grading with full evaluation"""
    
    # Sample conversation
    sample_conversation = Conversation(messages=[
        Message.system("You are a helpful software engineering assistant."),
        Message.user("Please help me implement a binary search algorithm."),
        Message.assistant("I'll help you implement binary search. Let me start by reading the existing code."),
        # ... more messages
    ])
    
    # Get detailed evaluation
    evaluation = await evaluate_conversation_detailed(
        conversation=sample_conversation,
        rubric_prompt=SWE_BEHAVIOR_RUBRIC,
        rubric_name="swe_behavior"
    )
    
    print(f"Overall Quality: {'Good' if evaluation.is_good_conversation else 'Bad'}")
    print(f"Overall Score: {evaluation.overall_score:.2f}")
    print(f"Feedback: {evaluation.overall_feedback}")
    
    print("\nCriterion Scores:")
    for criterion, details in evaluation.criterion_scores.items():
        print(f"  {criterion}: {details['score']:.2f} - {details['explanation']}")
    
    return evaluation


async def batch_conversation_grading(conversations: List[Conversation], rubric_name: str = "swe_behavior"):
    """Example of grading multiple conversations"""
    
    # Select rubric based on name
    rubric_map = {
        "swe_behavior": SWE_BEHAVIOR_RUBRIC,
        "simple": SIMPLE_SWE_RUBRIC,
        "antipattern": SWE_ANTIPATTERN_RUBRIC,
        "comprehensive": COMPREHENSIVE_SWE_RUBRIC
    }
    
    rubric_prompt = rubric_map.get(rubric_name, SWE_BEHAVIOR_RUBRIC)
    
    grader = ConversationGrader()
    results = []
    
    for i, conversation in enumerate(conversations):
        print(f"Grading conversation {i+1}/{len(conversations)}...")
        
        try:
            evaluation = await grader.evaluate_conversation(
                conversation=conversation,
                rubric_prompt=rubric_prompt,
                rubric_name=rubric_name
            )
            results.append(evaluation)
            
        except Exception as e:
            print(f"Failed to grade conversation {i+1}: {e}")
            # Create a failed evaluation
            from .conversation_grader import ConversationEvaluation
            failed_eval = ConversationEvaluation(
                is_good_conversation=False,
                overall_score=0.0,
                criterion_scores={},
                overall_feedback=f"Grading failed: {e}",
                rubric_name=rubric_name
            )
            results.append(failed_eval)
    
    # Calculate summary statistics
    good_conversations = sum(1 for r in results if r.is_good_conversation)
    avg_score = sum(r.overall_score for r in results) / len(results) if results else 0
    
    print(f"\nBatch Grading Results:")
    print(f"Total conversations: {len(conversations)}")
    print(f"Good conversations: {good_conversations}")
    print(f"Success rate: {good_conversations/len(conversations)*100:.1f}%")
    print(f"Average score: {avg_score:.2f}")
    
    return results


async def filter_good_conversations(conversations: List[Conversation], min_score: float = 0.7):
    """Filter conversations to keep only high-quality ones"""
    
    grader = ConversationGrader()
    good_conversations = []
    
    for conversation in conversations:
        evaluation = await grader.evaluate_conversation(
            conversation=conversation,
            rubric_prompt=SWE_BEHAVIOR_RUBRIC,
            rubric_name="swe_behavior"
        )
        
        if evaluation.is_good_conversation and evaluation.overall_score >= min_score:
            good_conversations.append(conversation)
    
    return good_conversations


# Integration example for the main data generation pipeline
async def grade_generated_conversations(data_file_path: str, output_file_path: str):
    """
    Example of integrating grading into the data generation pipeline.
    
    This would typically be called after generating conversations to filter
    out low-quality ones before including them in training data.
    """
    import json
    
    # Load generated conversations
    conversations = []
    with open(data_file_path, 'r') as f:
        for line in f:
            data = json.loads(line)
            # Extract conversation from data format
            # conversation = Conversation.from_dict(data['conversation'])  # Adjust based on your data format
            # conversations.append(conversation)
            pass
    
    print(f"Loaded {len(conversations)} conversations for grading")
    
    # Grade all conversations
    grading_results = await batch_conversation_grading(conversations, rubric_name="swe_behavior")
    
    # Filter and save high-quality conversations
    high_quality_data = []
    for i, (conversation, evaluation) in enumerate(zip(conversations, grading_results)):
        if evaluation.is_good_conversation and evaluation.overall_score >= 0.7:
            # Create data entry with grading metadata
            data_entry = {
                "conversation": conversation.model_dump(),  # Adjust based on your serialization
                "grading": {
                    "rubric_name": evaluation.rubric_name,
                    "overall_score": evaluation.overall_score,
                    "is_good_conversation": evaluation.is_good_conversation,
                    "criterion_scores": evaluation.criterion_scores,
                    "feedback": evaluation.overall_feedback
                }
            }
            high_quality_data.append(data_entry)
    
    # Save filtered conversations
    with open(output_file_path, 'w') as f:
        for data_entry in high_quality_data:
            f.write(json.dumps(data_entry) + '\n')
    
    print(f"Saved {len(high_quality_data)} high-quality conversations to {output_file_path}")
    return high_quality_data


if __name__ == "__main__":
    # Run examples
    print("Running basic grading example...")
    asyncio.run(example_basic_grading())
    
    print("\nRunning detailed grading example...")
    asyncio.run(example_detailed_grading())
