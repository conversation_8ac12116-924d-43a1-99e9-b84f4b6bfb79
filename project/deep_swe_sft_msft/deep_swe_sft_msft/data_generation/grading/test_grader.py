"""
Test script for the conversation grader.

This script tests the basic functionality of the grader to ensure it works correctly.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import the grading module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chat import Conversation, Message, Role
from grading import (
    ConversationGrader,
    grade_conversation,
    evaluate_conversation_detailed,
    SWE_BEHAVIOR_RUBRIC
)


def create_good_conversation_example():
    """Create an example of a good SWE conversation"""
    return Conversation(messages=[
        Message.system("You are a helpful software engineering assistant with access to development tools."),
        Message.user("I need to fix a bug in my Python function that's supposed to return a list but returns None."),
        Message.assistant("I'll help you fix that bug. Let me examine the code to understand the issue.", recipient="str_replace_editor"),
        Message.tool("Here's the content of the file:\n\ndef process_data(items):\n    if not items:\n        return  # BUG: should return []\n    return [item.upper() for item in items]", author_name="str_replace_editor"),
        Message.assistant("I found the bug! The function returns None when items is empty instead of returning an empty list. Let me fix this.", recipient="str_replace_editor"),
        Message.tool("File updated successfully.", author_name="str_replace_editor"),
        Message.assistant("The bug has been fixed. The function now properly returns an empty list when no items are provided, which maintains the expected return type.", channel="final", end_turn=True)
    ])


def create_bad_conversation_example():
    """Create an example of a bad SWE conversation with multiple issues"""
    return Conversation(messages=[
        Message.system("You are a helpful software engineering assistant."),
        Message.user("I need to fix a bug in my code."),
        Message.assistant("Let me look at your code.", recipient="str_replace_editor"),
        Message.tool("Error: File path not specified", author_name="str_replace_editor"),
        Message.assistant("Let me try again.", recipient="str_replace_editor"),
        Message.tool("Error: Invalid function call syntax", author_name="str_replace_editor"),
        Message.assistant("Let me read the file.", recipient="str_replace_editor"),
        Message.tool("def process_data(items): return items", author_name="str_replace_editor"),
        Message.assistant("Let me read the file again.", recipient="str_replace_editor"),
        Message.tool("def process_data(items): return items", author_name="str_replace_editor"),
        Message.assistant("Let me check another file.", recipient="str_replace_editor"),
        Message.tool("Error: Function call failed", author_name="str_replace_editor"),
        Message.assistant("I'm not sure how to help.", channel="final", end_turn=True)
    ])


async def test_basic_grading():
    """Test basic grading functionality"""
    print("Testing basic grading functionality...")
    
    # Test good conversation
    good_conv = create_good_conversation_example()
    is_good = await grade_conversation(good_conv, SWE_BEHAVIOR_RUBRIC, "simple_test")
    print(f"Good conversation result: {is_good} (expected: True)")
    
    # Test bad conversation  
    bad_conv = create_bad_conversation_example()
    is_bad = await grade_conversation(bad_conv, SWE_BEHAVIOR_RUBRIC, "simple_test")
    print(f"Bad conversation result: {is_bad} (expected: False)")
    
    return is_good and not is_bad


async def test_detailed_grading():
    """Test detailed grading functionality"""
    print("\nTesting detailed grading functionality...")
    
    good_conv = create_good_conversation_example()
    evaluation = await evaluate_conversation_detailed(good_conv, SWE_BEHAVIOR_RUBRIC, "swe_behavior_test")
    
    print(f"Evaluation result: {evaluation.is_good_conversation}")
    print(f"Overall score: {evaluation.overall_score:.2f} (fraction of criteria passed)")
    print(f"Rubric: {evaluation.rubric_name}")
    print(f"Feedback: {evaluation.overall_feedback}")
    
    if evaluation.criterion_scores:
        print("Criterion results (binary pass/fail):")
        for criterion, details in evaluation.criterion_scores.items():
            passed = details.get('passed', False)
            explanation = details.get('explanation', 'No explanation')
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"  {criterion}: {status} - {explanation}")
    
    return evaluation


async def test_grader_class():
    """Test the ConversationGrader class directly"""
    print("\nTesting ConversationGrader class...")
    
    grader = ConversationGrader()
    conv = create_good_conversation_example()
    
    evaluation = await grader.evaluate_conversation(conv, SWE_BEHAVIOR_RUBRIC, "class_test")
    print(f"Class test result: {evaluation.is_good_conversation}")
    
    return evaluation


async def run_all_tests():
    """Run all tests"""
    print("Starting conversation grader tests...\n")
    
    try:
        # Test basic functionality
        basic_result = await test_basic_grading()
        print(f"Basic grading test: {'PASSED' if basic_result else 'FAILED'}")
        
        # Test detailed functionality
        detailed_result = await test_detailed_grading()
        print(f"Detailed grading test: {'PASSED' if detailed_result.is_good_conversation else 'FAILED'}")
        
        # Test class functionality
        class_result = await test_grader_class()
        print(f"Class grading test: {'PASSED' if class_result.is_good_conversation else 'FAILED'}")
        
        print("\nAll tests completed!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Note: This test requires the actual message completer infrastructure to be available
    # In a real environment, you would run this where the BusTokenCompleter and other
    # dependencies are properly configured
    
    print("Conversation Grader Test Script")
    print("=" * 40)
    print("Note: This test requires proper infrastructure setup (BusTokenCompleter, etc.)")
    print("Run this in your actual development environment where all dependencies are available.")
    print()
    
    # Uncomment the following line to run tests in a proper environment:
    asyncio.run(run_all_tests())
    
    print("Test script loaded successfully. Call asyncio.run(run_all_tests()) to execute tests.")
