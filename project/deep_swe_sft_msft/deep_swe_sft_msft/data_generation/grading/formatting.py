"""
Formatting utilities for conversation grading output.

This module provides utilities for formatting grading results in various formats
for analysis, reporting, and debugging purposes.
"""

import json
from typing import List, Dict, Any
from datetime import datetime

from .conversation_grader import ConversationEvaluation


def format_evaluation_summary(evaluation: ConversationEvaluation) -> str:
    """Format a single evaluation as a summary string"""
    status = "✅ GOOD" if evaluation.is_good_conversation else "❌ BAD"
    score = f"{evaluation.overall_score:.2f}"
    
    summary = f"{status} | Score: {score} | Rubric: {evaluation.rubric_name}"
    if evaluation.overall_feedback:
        summary += f"\nFeedback: {evaluation.overall_feedback}"
    
    return summary


def format_evaluation_detailed(evaluation: ConversationEvaluation) -> str:
    """Format a single evaluation with detailed criterion breakdown"""
    header = f"{'='*60}\n"
    header += f"CONVERSATION EVALUATION REPORT\n"
    header += f"{'='*60}\n"
    header += f"Rubric: {evaluation.rubric_name}\n"
    header += f"Overall Result: {'✅ GOOD' if evaluation.is_good_conversation else '❌ BAD'}\n"
    header += f"Overall Score: {evaluation.overall_score:.2f}/1.0 (fraction of criteria passed)\n"
    header += f"Timestamp: {datetime.now().isoformat()}\n"
    header += f"{'-'*60}\n"
    
    criteria_section = "CRITERION BREAKDOWN (Binary Pass/Fail):\n"
    if evaluation.criterion_scores:
        for criterion, details in evaluation.criterion_scores.items():
            passed = details.get('passed', False)
            explanation = details.get('explanation', 'No explanation provided')
            status = "✅ PASS" if passed else "❌ FAIL"
            criteria_section += f"\n• {criterion}: {status}\n"
            criteria_section += f"  {explanation}\n"
    else:
        criteria_section += "No criterion details available.\n"
    
    feedback_section = f"\n{'-'*60}\n"
    feedback_section += f"OVERALL FEEDBACK:\n{evaluation.overall_feedback}\n"
    feedback_section += f"{'='*60}\n"
    
    return header + criteria_section + feedback_section


def format_batch_summary(evaluations: List[ConversationEvaluation]) -> str:
    """Format a summary of multiple evaluations"""
    if not evaluations:
        return "No evaluations to summarize."
    
    total = len(evaluations)
    good = sum(1 for e in evaluations if e.is_good_conversation)
    bad = total - good
    
    avg_score = sum(e.overall_score for e in evaluations) / total
    min_score = min(e.overall_score for e in evaluations)
    max_score = max(e.overall_score for e in evaluations)
    
    # Count by rubric
    rubric_counts = {}
    for e in evaluations:
        rubric_counts[e.rubric_name] = rubric_counts.get(e.rubric_name, 0) + 1
    
    summary = f"BATCH EVALUATION SUMMARY\n"
    summary += f"{'='*40}\n"
    summary += f"Total Conversations: {total}\n"
    summary += f"Good Conversations: {good} ({good/total*100:.1f}%)\n"
    summary += f"Bad Conversations: {bad} ({bad/total*100:.1f}%)\n"
    summary += f"\nScore Statistics:\n"
    summary += f"  Average: {avg_score:.2f}\n"
    summary += f"  Range: {min_score:.2f} - {max_score:.2f}\n"
    summary += f"\nRubrics Used:\n"
    for rubric, count in rubric_counts.items():
        summary += f"  {rubric}: {count} conversations\n"
    summary += f"{'='*40}\n"
    
    return summary


def export_evaluations_json(evaluations: List[ConversationEvaluation], filename: str):
    """Export evaluations to JSON file"""
    data = []
    for evaluation in evaluations:
        data.append({
            'is_good_conversation': evaluation.is_good_conversation,
            'overall_score': evaluation.overall_score,
            'criterion_scores': evaluation.criterion_scores,
            'overall_feedback': evaluation.overall_feedback,
            'rubric_name': evaluation.rubric_name,
            'timestamp': datetime.now().isoformat()
        })
    
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)
    
    print(f"Exported {len(evaluations)} evaluations to {filename}")


def export_evaluations_csv(evaluations: List[ConversationEvaluation], filename: str):
    """Export evaluations to CSV file"""
    import csv
    
    # Collect all unique criterion names
    all_criteria = set()
    for evaluation in evaluations:
        all_criteria.update(evaluation.criterion_scores.keys())
    all_criteria = sorted(list(all_criteria))
    
    # Create CSV headers
    headers = ['rubric_name', 'is_good_conversation', 'overall_score', 'overall_feedback']
    for criterion in all_criteria:
        headers.extend([f'{criterion}_passed', f'{criterion}_explanation'])
    
    with open(filename, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        
        for evaluation in evaluations:
            row = [
                evaluation.rubric_name,
                evaluation.is_good_conversation,
                evaluation.overall_score,
                evaluation.overall_feedback
            ]
            
            for criterion in all_criteria:
                if criterion in evaluation.criterion_scores:
                    details = evaluation.criterion_scores[criterion]
                    row.extend([
                        details.get('passed', False),
                        details.get('explanation', '')
                    ])
                else:
                    row.extend([False, ''])
            
            writer.writerow(row)
    
    print(f"Exported {len(evaluations)} evaluations to {filename}")


def create_evaluation_report(evaluations: List[ConversationEvaluation], output_file: str):
    """Create a comprehensive evaluation report"""
    with open(output_file, 'w') as f:
        # Write summary
        f.write(format_batch_summary(evaluations))
        f.write("\n\n")
        
        # Write individual evaluations
        f.write("INDIVIDUAL EVALUATION DETAILS\n")
        f.write("="*80 + "\n\n")
        
        for i, evaluation in enumerate(evaluations, 1):
            f.write(f"CONVERSATION {i}\n")
            f.write(format_evaluation_detailed(evaluation))
            f.write("\n\n")
    
    print(f"Created comprehensive evaluation report: {output_file}")


# Utility functions for specific analysis
def analyze_criterion_performance(evaluations: List[ConversationEvaluation], criterion_name: str) -> Dict[str, Any]:
    """Analyze performance on a specific criterion across all evaluations"""
    passed_count = 0
    total_count = 0
    explanations = []
    
    for evaluation in evaluations:
        if criterion_name in evaluation.criterion_scores:
            details = evaluation.criterion_scores[criterion_name]
            passed = details.get('passed', False)
            explanation = details.get('explanation', '')
            
            total_count += 1
            if passed:
                passed_count += 1
            explanations.append(explanation)
    
    if total_count == 0:
        return {'error': f'No data found for criterion: {criterion_name}'}
    
    pass_rate = passed_count / total_count
    
    return {
        'criterion': criterion_name,
        'total_evaluations': total_count,
        'passed_count': passed_count,
        'failed_count': total_count - passed_count,
        'pass_rate': pass_rate,
        'sample_explanations': explanations[:5]  # First 5 explanations as examples
    }


def find_problematic_conversations(evaluations: List[ConversationEvaluation]) -> List[ConversationEvaluation]:
    """Find conversations that failed any criteria (since all must pass for good conversation)"""
    problematic = []
    
    for evaluation in evaluations:
        if not evaluation.is_good_conversation:
            problematic.append(evaluation)
    
    return problematic