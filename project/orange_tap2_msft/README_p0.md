# `orange_tap2_msft`

1. get permission or https://github.com/github/sweagentd/tree/main and https://dev.azure.com/project-argos/Mimco/_git/glass

### setup sweagentd in corpnet devbox
1. open wsl
2. gh repo clone github/sweagentd -- --branch piyushmadan/orange_tap2 ~/code/sweagentd
3. cd ~/code/sweagentd/runtime/
   npm install
4. setup node22: download from [swang-test/node-v22.14.0-linux-x64.tar.xz](https://codemodeldata.blob.core.windows.net/swe-model-training-data/swang-test/node-v22.14.0-linux-x64.tar.xz) to your wsl
   tar -xf node-v22.14.0-linux-x64.tar.xz --strip-components=1 -C /usr
   node -v [should be 22]
5. sudo apt update
   sudo apt install -y golang-go
6. mac:
brew install nvm
mkdir ~/.nvm
add below two lines to ~/.zprofile
export NVM_DIR="$HOME/.nvm"
[ -s "$(brew --prefix nvm)/nvm.sh" ] && \. "$(brew --prefix nvm)/nvm.sh"
source ~/.zprofile
nvm install 22.14.0
nvm alias default 22.14.0
nvm use 22.14.0
brew update
brew install go
go version
npm install playwright@latest
npm install @playwright/mcp@latest
npx playwright install --with-deps
npx @playwright/mcp@latest --port 1234 &


### setup twapi in orange devbox (optional), you can get your own or reuse other exisitng endpoints
1. get gpu vm
2. checkout [glass](https://dev.azure.com/project-argos/Mimco/_git/glass) orange_tap2 branch
3. start engine
RUST_BACKTRACE=1 nohup python -m harmony_scripts.engines.start_engine \
--name oswe-mini-v2-engine \
--mode=optimal \
--snapshot_path="az://orngscuscresco/twapi/mini/e/zhendongwang-data-mix-padawan-ivt16k-64x32-rm-run2/policy/step_000040/" \
--is_multimodal=False \
--gpu_kind=H100 \
--renderer_name="harmony_v4.0.15_berry_v3_1mil_orion_lpe" \
--restartable \
--extra_config_string="falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 ixf_kv_block_unit_size=2048 n_op_shards=2 pipe_depth=4 n_replicas=1" \
--cluster=local \
--use_bus_v2=False \
--n_replicas=1 \
> my_log.log 2>&1 &
4. oaipkg install qstar
4. start ochestrator
   cd ~/code/glass/orange_tap2_msft/orchestrator && uvicorn fast_api:app --reload --host 0.0.0.0 --port 8009 &
5. create research endpoint and mapping to your twapi:
   orange_research_endpoint_ctl publish bus-o4-15-model-0 8009 /v1/inference
6. test your endpoint with data/test_script.txt method

### start test
1. go to corpnet devbox, cd ~/code/sweagentd/runtime
2. az login, with your ms id
3. gh auth login, with your gh id with permission to ghcpd repos
4. update .env.local, as data/.env.local, just need to change AZURE_OPENAI_ORANGE_TAP_ENDPOINT and AZURE_OPENAI_ORANGE_TAP_SERVICE_URL
5. run test
   npm test test/evals/basic.test.ts
   COPILOT_MCP_ENABLED=true npm test test/evals/webDevE2E.test.ts
6. get log:
   sweagentd log: /tmp/sweagent-evals/*/cpd.log
   model log: ~/code/glass/project/orange_tap2_msft/orchestrator/log

