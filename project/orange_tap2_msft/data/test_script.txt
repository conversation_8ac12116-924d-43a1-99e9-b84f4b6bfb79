az account get-access-token --resource 2a750fd4-529b-4678-b332-6331e201131c --query accessToken -o tsv
 
curl -k "https://104.210.200.242:443/v1/inference" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "User-Agent: AzureOpenAIClient/JS 4.96.0" \
  -H "X-Stainless-Lang: js" \
  -H "X-Stainless-Package-Version: 4.96.0" \
  -H "X-Stainless-OS: MacOS" \
  -H "X-Stainless-Arch: arm64" \
  -H "X-Stainless-Runtime: node" \
  -H "X-Stainless-Runtime-Version: v24.3.0" \
  -H "Authorization: Bearer {token here}" \
  -H "X-Stainless-Retry-Count: 0" \
  -H "X-Stainless-Timeout: 600" \
  -H "SERVICE_URL: http://bus-o4-15-model-0-svc-8009.swang.svc.cluster.local:8009" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {"role": "system", "content": "You are an AI assistant."},
      {"role": "user", "content": "list dirs", "copilot_cache_control": {"type": "ephemeral"}}
    ],
    "temperature": 0,
    "top_p": 0.95,
    "frequency_penalty": 0,
    "presence_penalty": 0,
    "parallel_tool_calls": false,
    "tools": [
        {
            "type": "function",
            "function": {
                "name": "bash",
                "description": "Runs a bash command in an interactive bash",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "description": "The bash command and arguments to run...."
                        }
                    },
                    "required": [
                        "command",
                        "sessionId",
                        "async"
                    ]
                }
            }
        }
    ]
}'



curl -k "https://*************:443/v1/inference" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "User-Agent: AzureOpenAIClient/JS 4.96.0" \
  -H "X-Stainless-Lang: js" \
  -H "X-Stainless-Package-Version: 4.96.0" \
  -H "X-Stainless-OS: MacOS" \
  -H "X-Stainless-Arch: arm64" \
  -H "X-Stainless-Runtime: node" \
  -H "X-Stainless-Runtime-Version: v24.3.0" \
  -H "Authorization: Bearer {token here}" \
  -H "X-Stainless-Retry-Count: 0" \
  -H "X-Stainless-Timeout: 600" \
  -H "SERVICE_URL: http://gargamit-devbox-8cpu-0-svc-8001.gargamit.svc.cluster.local:8001" \
  -d '{
    "model": "gpt-4o",
    "messages": [
      {"role": "system", "content": "You are an AI assistant."},
      {"role": "user", "content": "list dirs", "copilot_cache_control": {"type": "ephemeral"}}
    ],
    "temperature": 0,
    "top_p": 0.95,
    "frequency_penalty": 0,
    "presence_penalty": 0,
    "parallel_tool_calls": false,
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "bash",
          "description": "Runs a bash command in an interactive bash",
          "parameters": {
            "type": "object",
            "properties": {
              "command": {
                "type": "string",
                "description": "The bash command and arguments to run...."
              }
            },
            "required": [
              "command",
              "sessionId",
              "async"
            ]
          }
        }
      }]
}'


curl -X POST http://localhost:8009/v1/inference   -H "Content-Type: application/json"   -d '{
    "model": "gpt-4o",
    "messages": [
      {"role": "system", "content": "You are an AI assistant."},
      {"role": "user", "content": "list dirs", "copilot_cache_control": {"type": "ephemeral"}}
    ],
    "temperature": 0,
    "top_p": 0.95,
    "frequency_penalty": 0,
    "presence_penalty": 0,
    "parallel_tool_calls": false,
    "tools": [
        {
            "type": "function",
            "function": {
                "name": "bash",
                "description": "Runs a bash command in an interactive bash",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "description": "The bash command and arguments to run...."
                        }
                    },
                    "required": [
                        "command",
                        "sessionId",
                        "async"
                    ]
                }
            }
        }
    ]
}'