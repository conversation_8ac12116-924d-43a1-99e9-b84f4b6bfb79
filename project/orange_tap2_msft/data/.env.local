AZURE_OPENAI_ORANGE_TAP_BUS_RENDERER=harmony_v4.0.15_berry_v3_1mil_orion_lpe
AZURE_OPENAI_ORANGE_TAP_BUS_USER=stub
AZURE_OPENAI_ORANGE_TAP_BUS_SNAPSHOT=stub
 

# your research endpoint here, or just use this to test
AZURE_OPENAI_ORANGE_TAP_ENDPOINT=https://104.210.200.242:443
AZURE_OPENAI_ORANGE_TAP_RESOURCE_ID=2a750fd4-529b-4678-b332-6331e201131c
# your twapi engine here or just use this for test
AZURE_OPENAI_ORANGE_TAP_SERVICE_URL=http://bus-o4-15-model-0-svc-8009.swang.svc.cluster.local:8009
NODE_TLS_REJECT_UNAUTHORIZED=0
 
 
NIGHTLY_TEST_CI=true
EVAL_AGENT_KIND=sweagent-openai
 
COPILOT_AGENT_MODEL=sweagent-openai
# There is an enforced check on following two env variable for now. We will check with GitHub folks it is okay to modify superclass
EVAL_MODEL=gpt-4o
AZURE_OPENAI_API_VERSION=2024-10-21
 
 
# "fix" or "fix-pr-comment"
COPILOT_AGENT_ACTION=fix
COPILOT_AGENT_PUSH=false
 
GITHUB_HOST=github.com
 
# Optional - For use with CAPI
GITHUB_COPILOT_INTEGRATION_ID=copilot-developer-dev
CONTAINER_IMAGE=ghcr.io/github/sweagentd/runtime:latest
 
 
COPILOT_INTEGRATION_ID="copilot-developer-dev"
GITHUB_COPILOT_INTEGRATION_ID="copilot-developer-dev"