import json
from typing import Op<PERSON>, Dict, Any
import uuid
import time
import os
import re

from chat import chat
from fastapi import FastAP<PERSON>, HTTPEx<PERSON>, Header
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from pydantic import BaseModel, Field
from legacy_rest_token_completer import LegacyRest<PERSON>okenCompleter
from token_completer import CompleterBackend
from turn_completer.berry_turn_completer import BerryMultiMessageTurnCompleter
from message_completer.message_completer import Parse<PERSON>rrorMode
from bus_token_completer import BusTokenCompleter
from message_completer.token_message_completer import TokenMessageCompleter
from chat.render.renderer_registry import get_renderer

# Add these imports for constrained extension support
from qstar.common.tools import constraint_machine_spec
from qstar.common.tools.renderer_worker import (
    get_disc_score_token,
    get_end_of_tool_message_token,
    get_end_of_turn_token,
)

from collections import OrderedDict
import asyncio
from filelock import FileLock
from datetime import datetime

app = FastAPI()

CONVO_CACHE: OrderedDict[str, (chat.Conversation, int)] = OrderedDict()
CONVO_CACHE_LOCK = asyncio.Lock()
CONVO_CACHE_FILE_LOCK = FileLock("/tmp/convo_cache.lock")
MAX_CACHE_SIZE = 12800

# Default configuration
DEFAULT_RENDERER_NAME = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"
DEFAULT_BUS_USER = "evaluation"
DEFAULT_BUS_SNAPSHOT = "az://orngscuscresco/twapi/mini/e/zhendongwang-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run2-continue2/policy/step_000120"
USE_BUS = False
MAX_YIELD = 256
MAX_STEP = 100

def get_render_constrained_extension(renderer_name: str, tools: tuple):
    """Get the constrained extension for the renderer."""
    # Ensure tools is a tuple for hashability
    if not isinstance(tools, tuple):
        tools = tuple(tools)

    extension = constraint_machine_spec.constraint_machine_extension(
        harmony_renderer_name=renderer_name,
        encoding_name="orion_200k",
        final_token_sequences=frozenset([
            (get_end_of_tool_message_token(renderer_name),),
            (get_end_of_turn_token(renderer_name), get_disc_score_token(renderer_name)),
        ]),
        json_eot_tokens=frozenset([
            get_end_of_tool_message_token(renderer_name),
            get_end_of_turn_token(renderer_name),
        ]),
        tools=tools,
        channels=("analysis", "final"),
        response_format_names=(),
        enable_json_constraining=False,
        none_channel_allowed=False,
    )
    return extension

def extract_function_names_from_tools(tools_data: Any) -> tuple:
    """Extract function names from tools data for constrained extension."""
    if not tools_data:
        return tuple()

    function_names = []

    # Handle different input types
    if isinstance(tools_data, str):
        try:
            tools_data = json.loads(tools_data)
        except json.JSONDecodeError:
            return tuple()

    # Extract tools list
    tools_list = []
    if isinstance(tools_data, dict):
        if "tools" in tools_data:
            tools_list = tools_data["tools"]
        elif "function" in tools_data:
            tools_list = [tools_data]
    elif isinstance(tools_data, list):
        tools_list = tools_data

    # Process each tool to extract function names
    for tool in tools_list:
        if isinstance(tool, dict) and "function" in tool:
            function_data = tool["function"]
            function_name = function_data.get("name", "unknown")
            function_names.append(f"functions.{function_name}")

    return tuple(function_names)

def extract_tool_instructions(tools_data: Any) -> Dict[str, str]:
    """Extract function name and description/instructions from tools data."""
    tool_instructions = {}

    # Handle different input types
    if isinstance(tools_data, str):
        try:
            tools_data = json.loads(tools_data)
        except json.JSONDecodeError:
            print("Error: Invalid JSON string provided")
            return {}

    # Extract tools list
    tools_list = []
    if isinstance(tools_data, dict):
        if "tools" in tools_data:
            tools_list = tools_data["tools"]
        elif "function" in tools_data:
            tools_list = [tools_data]
    elif isinstance(tools_data, list):
        tools_list = tools_data

    # Process each tool
    for tool in tools_list:
        if isinstance(tool, dict) and "function" in tool:
            function_data = tool["function"]
            tool_name = function_data.get("name", "unknown")
            description = function_data.get("description", "")
            parameters = function_data.get("parameters", {})

            lines = []

            for dline in description.splitlines():
                lines.append(f"// {dline.strip()}")

            lines.append(f"type {tool_name} = (_: {{")

            props = parameters.get("properties", {})
            required = set(parameters.get("required", []))

            for prop, details in props.items():
                if "enum" in details:
                    # union of literal types
                    enum_vals = details["enum"]
                    ts_type = " | ".join(f'"{v}"' for v in enum_vals)
                else:
                    ts_type = details.get("type", "any")

                optional = "" if prop in required else "?"
                prop_desc = details.get("description", "").replace("\n", " ").strip()
                lines.append(f"// {prop_desc}")
                lines.append(f"{prop}{optional}: {ts_type},")
            lines.append("}) => any;\n")

            tool_instructions[tool_name] = "\n".join(lines)

    return tool_instructions

def clean_escaped_quotes(content: str) -> str:
    """Clean escaped quotes from content strings."""
    if isinstance(content, str):
        content = content.replace("\\'", "'").replace('\\"', '"')
    return content

def convert_to_azure_openai_format(messages, cache_key, model_name="gpt-4o"):
    """Convert internal message format to Azure OpenAI API format."""
    # Process all messages and consolidate into a single choice
    if not messages:
        choices = [{
            "content_filter_results": {},
            "finish_reason": "stop",
            "index": 0,
            "logprobs": None,
            "message": {
                "id": cache_key,
                "annotations": [],
                "content": "Error: No valid response generated",
                "refusal": None,
                "role": "assistant"
            }
        }]
        usage = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
    else:
        # Find the last tool call message (code content type)
        tool_call_message = None
        analysis_text_parts = []
        if messages[-1].end_turn:
            is_last_step = True
        else:
            is_last_step = False

        # Extract token usage information from messages metadata
        prompt_tokens = 0
        completion_tokens = 0

        # Collect analysis channel text content and find tool calls
        for message in messages:
            if hasattr(message, 'content'):
                content_obj = message.content

                # Extract token usage from message metadata
                if hasattr(message, 'metadata') and message.metadata:
                    if 'message_content_token_count' in message.metadata:
                        completion_tokens += message.metadata['message_content_token_count']
                    if 'first_content_token_index' in message.metadata:
                        # The first_content_token_index indicates where content starts in the total token sequence
                        # This can help us estimate prompt tokens for the first message
                        if prompt_tokens == 0:  # Only set once for the first message
                            prompt_tokens = message.metadata['first_content_token_index']

                # Collect analysis channel text for cot_summary
                if (hasattr(message, 'channel') and message.channel == 'analysis' and
                    hasattr(content_obj, 'content_type') and content_obj.content_type == 'text'):
                    if hasattr(content_obj, 'parts'):
                        # Handle Text content with parts
                        text_parts = [str(part) for part in content_obj.parts]
                        analysis_text_parts.extend(text_parts)
                    elif hasattr(content_obj, 'text'):
                        analysis_text_parts.append(content_obj.text)

                # Find tool call (code content)
                if (hasattr(content_obj, 'content_type') and content_obj.content_type == 'code'):
                    tool_call_message = message

        # Calculate total tokens
        total_tokens = prompt_tokens + completion_tokens
        usage = {
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": total_tokens
        }

        # Build cot_summary from analysis text
        cot_summary = " ".join(analysis_text_parts).strip() if analysis_text_parts else ""

        # Determine the primary response
        if tool_call_message:
            # Handle tool call message
            content_obj = tool_call_message.content

            # Get the actual text content
            if hasattr(content_obj, 'text'):
                text_content = content_obj.text
            elif hasattr(content_obj, 'content'):
                text_content = content_obj.content
            elif hasattr(content_obj, '__str__'):
                text_content = str(content_obj)
            else:
                text_content = repr(content_obj)

            try:
                tool_call_content = json.loads(text_content)
                function_name = (
                    tool_call_message.recipient.split('.')[-1]
                    if hasattr(tool_call_message, 'recipient') and '.' in tool_call_message.recipient
                    else (tool_call_message.recipient if hasattr(tool_call_message, 'recipient') else "unknown")
                )


                choice = {
                    "content_filter_results": {},
                    "finish_reason": "stop" if is_last_step else "tool_calls",
                    "index": 0,
                    "logprobs": None,
                    "message": {
                        "id": cache_key,
                        "annotations": [],
                        "content": None,
                        "cot_id": f"cot_{str(uuid.uuid4())}",
                        "cot_summary": cot_summary,
                        "refusal": None,
                        "role": "assistant",
                        "tool_calls": [{
                            "id": f"call_{str(uuid.uuid4()).replace('-', '')}",
                            "type": "function",
                            "function": {
                                "name": function_name,
                                "arguments": json.dumps(tool_call_content)
                            }
                        }]
                    }
                }
            except (json.JSONDecodeError, TypeError):
                # Fallback to text content if JSON parsing fails
                choice = {
                    "content_filter_results": {},
                    "finish_reason": "stop",
                    "index": 0,
                    "logprobs": None,
                    "message": {
                        "id": cache_key,
                        "annotations": [],
                        "content": text_content,
                        "refusal": None,
                        "role": "assistant"
                    }
                }
        else:
            # No tool call found, use the last message or consolidated text
            # if analysis_text_parts:
            #    # Use the analysis text if available
            #    content_text = cot_summary
            #else:
            if True:
                # Fall back to last message content
                message = messages[-1]
                content_obj = message.content

                if hasattr(content_obj, 'text'):
                    content_text = content_obj.text
                elif hasattr(content_obj, 'parts'):
                    content_text = "".join(str(part) for part in content_obj.parts)
                elif hasattr(content_obj, '__str__'):
                    content_text = str(content_obj)
                else:
                    content_text = repr(content_obj)

                # Check for system errors
                if (hasattr(content_obj, 'content_type') and
                    content_obj.content_type == "system_error"):
                    content_text = f"Error: {content_text}"

            choice = {
                "content_filter_results": {},
                "finish_reason": "stop",
                "index": 0,
                "logprobs": None,
                "message": {
                    "id": cache_key,
                    "annotations": [],
                    "content": content_text,
                    "refusal": None,
                    "role": "assistant"
                }
            }

        choices = [choice]

    return {
        "id": f"chatcmpl-{str(uuid.uuid4())}",
        "object": "chat.completion",
        "created": int(time.time()),
        "model": model_name,
        "choices": choices,
        "usage": usage,
        "system_fingerprint": None
    }

# Pydantic models for request validation
class InferenceRequest(BaseModel):
    """Request model for inference endpoint"""
    messages: list = Field(..., description="List of messages for completion")
    temperature: float = Field(..., description="Temperature for completion")
    tools: Optional[list] = Field(None, description="List of tools available for completion")
    model: Optional[str] = Field("gpt-4o", description="Model name")

    class Config:
        extra = "allow"

def get_bus_config_from_headers(
    bus_user: Optional[str] = None,
    bus_snapshot: Optional[str] = None,
    bus_renderer: Optional[str] = None
) -> Dict[str, str]:
    """Extract and validate bus configuration from headers, applying defaults where needed"""
    return {
        "user": bus_user or DEFAULT_BUS_USER,
        "snapshot": bus_snapshot or DEFAULT_BUS_SNAPSHOT,
        "renderer": bus_renderer or DEFAULT_RENDERER_NAME
    }

def process_message_by_role(input_message: dict, tools: Optional[Dict[str, str]]) -> chat.Message:
    """Process input message based on role and create appropriate chat.Message"""
    role = input_message["role"]
    input_message_without_role = {k: v for k, v in input_message.items() if k != "role"}

    # Define allowed keys for input_message_without_role
    ALLOWED_MESSAGE_KEYS = [
        "content", "instructions", "name", "tools_section", "tool_calls", "tool_call_id", "recipient", "cot_summary"
    ]
    # Only keep allowed keys
    input_message_without_role = {
        k: v for k, v in input_message_without_role.items()
        if k in ALLOWED_MESSAGE_KEYS
    }

    if role == "system":
        # Convert 'content' to 'instructions' if exists and clean escaped quotes
        if "content" in input_message_without_role:
            content = input_message_without_role.pop("content")
            cleaned_content = clean_escaped_quotes(content)
            input_message_without_role["instructions"] = cleaned_content
        if tools:
            tools_section = ["// Microsoft Padawan tool for code generation and editing\n// (container_tool, 1.2.0)\n// (lean_terminal, 1.0.0)\n// (caas, 2.3.0)\nnamespace functions {"]
            tools_section.extend(tools.values())
            tools_section.append("} // namespace functions")
            input_message_without_role["tools_section"] = "\n\n".join(tools_section)
        #return chat.Message.system(**input_message_without_role)
        return chat.Message.system(
                    model_identity_desc=input_message_without_role["instructions"],
                    tools_section={"functions": input_message_without_role["tools_section"]},
                    channel_config=chat.SystemChannelConfig(valid_channels=("analysis","final"), channel_required=True),
                    metadata=chat.SystemContentMetadata(reward_multiplier=768)
                )
    elif role == "user":
        if "content" in input_message_without_role:
            input_message_without_role["content"] = clean_escaped_quotes(
                input_message_without_role["content"]
            )
        return chat.Message.user(**input_message_without_role)

    elif role == "assistant":
        messages = []
        if "content" in input_message_without_role and input_message_without_role["content"]:
            input_message_without_role["content"] = clean_escaped_quotes(
                input_message_without_role["content"]
            )
            messages.append(chat.Message.assistant(
                    recipient="all",
                    content=chat.Text.from_string(input_message_without_role["content"]),
                    channel="analysis",
                    end_turn=False,
                    ))
        if "cot_summary" in input_message_without_role:
            input_message_without_role["cot_summary"] = clean_escaped_quotes(
                input_message_without_role["cot_summary"]
            )
            messages.append(chat.Message.assistant(
                    recipient="all",
                    content=chat.Text.from_string(input_message_without_role["cot_summary"]),
                    channel="analysis",
                    end_turn=False,
                    ))

        # Handle tool calls if present
        if "tool_calls" in input_message_without_role:
            tool_calls = input_message_without_role.pop("tool_calls")

            # Process each tool call and create appropriate messages
            for tool_call in tool_calls:
                if tool_call.get("type") == "function":
                    function_info = tool_call.get("function", {})
                    function_name = function_info.get("name", "unknown")
                    function_args = function_info.get("arguments", "{}")

                    # Parse arguments if they're a string
                    try:
                        if isinstance(function_args, str):
                            args_dict = json.loads(function_args)
                        else:
                            args_dict = function_args
                    except json.JSONDecodeError:
                        args_dict = {}

                    # Create tool call message with proper recipient format
                    recipient = f"functions.{function_name}"

                    # Create Code content for the tool call
                    code_content = chat.Code(
                        text=json.dumps(args_dict),
                        language=chat.Language.JSON
                    )

                    # Create the assistant message with tool call
                    tool_call_message = chat.Message.assistant(
                        content=code_content,
                        recipient=recipient
                    )
                    messages.append(tool_call_message)

            return messages[0] if len(messages) == 1 else messages
        else:
            # Regular assistant message without tool calls
            return chat.Message.assistant(**input_message_without_role)

    elif role == "tool":
        if "content" in input_message_without_role:
            input_message_without_role["content"] = clean_escaped_quotes(
                input_message_without_role["content"]
            )

        # Extract tool_call_id for author_name (required for tool messages)
        tool_call_id = input_message_without_role.pop("tool_call_id", "unknown_tool")

        # Create tool message with required author_name
        return chat.Message.tool(author_name=tool_call_id, **input_message_without_role)

    else:
        raise HTTPException(status_code=400, detail=f"Invalid message 'role': {role}")

@app.post("/v1/inference")
async def inference(
    request_data: InferenceRequest,
    x_bus_user: Optional[str] = Header(None, alias="X-Bus-User"),
    x_bus_snapshot: Optional[str] = Header(None, alias="X-Bus-Snapshot"),
    x_bus_renderer: Optional[str] = Header(None, alias="X-Bus-Renderer")
) -> JSONResponse:
    try:
        # Extract bus configuration from headers with defaults
        bus_config = get_bus_config_from_headers(x_bus_user, x_bus_snapshot, x_bus_renderer)

        # Extract function names for constrained extension (if tools provided)
        valid_function_tool_names = None
        if request_data.tools:
            valid_function_tool_names = extract_function_names_from_tools(request_data.tools)


        # Extract tool instructions from the request data
        tools = extract_tool_instructions(request_data.tools) if request_data.tools else None

        msgs = request_data.messages

        is_pr_user_input = False
        # New conversation
        renderer = get_renderer(bus_config["renderer"])
        if len(msgs) <= 2:
            initial_msgs = [process_message_by_role(m, tools) for m in msgs]
            convo = chat.Conversation(messages=initial_msgs)
            convo.metadata.header_yields_budget_total_override=MAX_YIELD
            convo.metadata.header_yields_budget_for_action=MAX_YIELD
            cache_key = str(convo.id)
            yield_number = 1
        else:
            cache_key = msgs[2].get("id")
            #print(CONVO_CACHE)
            #async with CONVO_CACHE_LOCK:
            with CONVO_CACHE_FILE_LOCK:
                if not cache_key or cache_key not in CONVO_CACHE:
                    raise HTTPException(status_code=400, detail="Conversation not found in cache")
                #convo, yield_number = CONVO_CACHE.pop(cache_key)
                convo, yield_number = CONVO_CACHE[cache_key]

            # Append only the latest message
            last_input = msgs[-1]
            new_input_msgs = process_message_by_role(last_input, None)
            if isinstance(new_input_msgs, list):
                convo = convo.with_suffix(*new_input_msgs)
            else:
                convo = convo.with_suffix(new_input_msgs)
            if last_input["role"] == "user":
                is_pr_user_input = True

        if USE_BUS:
            print(f"Using bus config: {bus_config}")

            token_completer_config = BusTokenCompleter.Config(
                topic_mode_or_user=bus_config["user"],
                topic_or_snapshot=bus_config["snapshot"],
            )

        else:
            os.environ["OPENAI_API_KEY"] = "dummy"
            print(f"Using fake bus config, only render is used: {bus_config}")
            print("Using legacy REST token completer configuration")
            token_completer_config = LegacyRestTokenCompleter.Config(
                api_base="http://localhost:5001/v1", #"http://localhost:5122/v1/inference",
                backend=CompleterBackend.TEXT_BACKEND,  # use TEXT_BACKEND for research (non-api.openai.com) engines
            )

        completion_params = {"temperature": request_data.temperature}

        # Add constrained extension if tools are provided
        if valid_function_tool_names:
            extension = get_render_constrained_extension(renderer.name, valid_function_tool_names)
            completion_params["extensions"] = [extension]
        berry_turn_completer_config = TokenMessageCompleter.Config(
            token_completer_config=token_completer_config,
            completion_params=completion_params,
            renderer=renderer,
            #"harmony_v4.0.15_berry_v3_1mil_orion_lpe",
            #renderer="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
        )
        message_completer = berry_turn_completer_config.build()

        # Get completion
        new_messages = await call_completer_with_retries(convo, message_completer, yield_number, is_pr_user_input)
        yield_number += 1
        convo = convo.with_suffix(*new_messages)

        #async with CONVO_CACHE_LOCK:
        with CONVO_CACHE_FILE_LOCK:
            if len(CONVO_CACHE) > MAX_CACHE_SIZE:
                CONVO_CACHE.popitem(last=False)
            CONVO_CACHE[cache_key] = (convo, yield_number)

        if len(new_messages) > 0 and new_messages[-1].end_turn:
            #unique_file_name = f"episode_{uuid.uuid4().hex}.jsonl"
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_file_name = f"episode_{timestamp}.jsonl"
            gpt_log_folder = "./log"
            write_messages_to_file(convo.messages, file_name=unique_file_name, folder=gpt_log_folder)
            print(f"Messages written to {os.path.join(gpt_log_folder, unique_file_name)}")

        # Build and return response
        aoai_response = convert_to_azure_openai_format(new_messages, cache_key, request_data.model)
        return JSONResponse(content=aoai_response)

    except HTTPException:
        raise
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON request")
    except Exception as e:
        print(f"Error in inference: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

ISSUE_TITLE_TO_NAME = {
    "Change the home page title and description": "does_call_str_replace",
    "Provide example input and output for the prompt": "can_create_a_test",
    "Add coverage to the mock capi-chat-completion-client.test.ts": "can_address_full_test",
    "Add support for functions": "lukehoban_full_function_impl",
    "Implement Beginnings of Application": "promptly_start_fre_repo",
}

def get_prefix_from_title(title_dict, title):
    for k, v in title_dict.items():
        if k in title:
            return v
    return ""

def write_messages_to_file(messages, file_name = "aaa", folder = "log"):
    if not isinstance(messages, list):
        messages = [messages]
    os.makedirs(folder, exist_ok=True)
    actual_file_name = file_name

    if len(messages) > 1:
        msg2 = messages[1]
        content_text = ""

        if isinstance(msg2, dict):
            # Support both raw dict and class with attributes
            content_text = msg2.get("content", {}).get("parts", [""])[0]
        else:
            content = getattr(msg2, "content", {})
            content_text = content.get("parts", [""])[0] if isinstance(content, dict) else str(msg2)

        match = re.search(r"<issue_title>\s*(.*?)\s*</issue_title>", content_text, re.DOTALL)
        if match:
            title = match.group(1).strip()
            prefix = get_prefix_from_title(ISSUE_TITLE_TO_NAME, title)
            if prefix:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                actual_file_name = f"{prefix}_{timestamp}.jsonl"

    with open(os.path.join(folder, actual_file_name), "w") as f:
        for m in messages:
            f.write(m.json() + "\n")

@app.get("/v1/config/defaults")
async def get_default_config():
    """Get current default bus configuration"""
    return {
        "user": DEFAULT_BUS_USER,
        "snapshot": DEFAULT_BUS_SNAPSHOT,
        "renderer": DEFAULT_RENDERER_NAME,
        "headers": {
            "X-Bus-User": "Override bus user/topic mode",
            "X-Bus-Snapshot": "Override bus snapshot/topic",
            "X-Bus-Renderer": "Override renderer name"
        }
    }

@app.get("/v1/config/headers")
async def get_header_info():
    """Get information about supported configuration headers"""
    return {
        "supported_headers": {
            "X-Bus-User": {
                "description": "Bus user/topic mode",
                "default": DEFAULT_BUS_USER,
                "example": "user name or topic mode"
            },
            "X-Bus-Snapshot": {
                "description": "Bus snapshot/topic",
                "default": DEFAULT_BUS_SNAPSHOT,
                "example": "az://orngcresco/models/snapshots/custom-snapshot"
            },
            "X-Bus-Renderer": {
                "description": "Renderer name",
                "default": DEFAULT_RENDERER_NAME,
                "example": "harmony_v4.0.15_berry_v3_16k_orion_text"
            }
        },
        "usage": "Include any of these headers in your request to override default bus configuration"
    }

async def call_completer_with_retries(convo, message_completer, yield_number, is_pr_user_input, retries=1):
    """Call message completer with retries for stability"""
    for completion_attempt in range(retries):
        try:
            end_header =  yield_number >= MAX_STEP
            if is_pr_user_input:
                end_header = False
            completion_params = {"conversations": [convo], "n": 1, "seed": 0, "end_header": end_header}
            completion = await message_completer.async_completion(**completion_params)

            choice = completion.choices[0]
            messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)
            return messages
        except Exception as e:
            if completion_attempt < retries - 1:
                print(f"Error in completion attempt {completion_attempt + 1}: {e}")
                time.sleep(1)
                continue
            else:
                raise ValueError(f"Error in completion after {retries} attempts: {e}")