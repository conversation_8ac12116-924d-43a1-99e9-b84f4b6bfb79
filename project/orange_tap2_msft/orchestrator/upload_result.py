import os
import re
import shutil
import subprocess
from pathlib import Path
from collections import defaultdict
import sys

# sample usage:
# python upload_result.py arm2_s130

def dedupe_and_upload(log_dir, output_dir):
    # Make sure paths are absolute
    log_dir = Path(log_dir).resolve()
    output_dir = Path(output_dir).resolve()

    # Create output folder
    output_dir.mkdir(parents=True, exist_ok=True)

    # Regex to match pattern: prefix_YYYYMMDD_HHMMSS.jsonl
    pattern = re.compile(r"(.+)_\d{8}_\d{6}\.jsonl")

    grouped_files = defaultdict(list)

    for file in log_dir.glob("*.jsonl"):
        match = pattern.match(file.name)
        if match:
            prefix = match.group(1)
            grouped_files[prefix].append(file)

    for prefix, files in grouped_files.items():
        # Sort by timestamp in filename
        files_sorted = sorted(files, key=lambda f: f.name, reverse=True)
        latest_file = files_sorted[0]
        shutil.copy2(latest_file, output_dir / latest_file.name)

    # Final az cpr command
    az_cmd = ["bbb", "cpr", str(output_dir), f"az://orngscuscresco/data/datasets/swe/eval/p0_test/{output_dir.name}"]
    print(f"Running: {' '.join(az_cmd)}")
    subprocess.run(az_cmd, check=True)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python dedupe_and_upload.py <output_folder_name>")
        sys.exit(1)

    folder_name = sys.argv[1]
    dedupe_and_upload(log_dir="log", output_dir=folder_name)