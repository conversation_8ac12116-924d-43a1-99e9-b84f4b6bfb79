from functools import partial
from typing import Any
import random

import berry
import chat

import caas_autograding.grader as caas_graders
import chz
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets.configs import DeepSWEDatasetConfig
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from deep_swe_msft.webdev_padawan_v2.datasets.caas_resource_config import CaasContainerResourceConfig
from deep_swe_msft.webdev_padawan_v2.datasets.setup import webdev_setup_fn_coreutils, webdev_setup_fn_coreutils_with_mcp_tools
from deep_swe_msft.webdev_padawan_v2.datasets.caas_padawan_tool import PadawanToolConfig
from deep_swe_msft.tools.caas_mix_tool import MixToolConfig
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CRESCO_STORAGE_NAME
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from qstar.sample_completers.basic_sample_completer import BasicSampleCompleter

from qstar.graders.taskgen_utils import TokenCompleterGraderService
from deep_swe.graders.code_plan_patch_grader import CodePlanConsistencyGrader, CodePlanPatchGrader
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK

from deep_swe_msft.padawan_graders.cotograder_utils import (
    COTOGRADER_RENDERER,
    COTOGRADER_CHZ_ARGV,
    CotoGraderService
)

from deep_swe_msft.webdev_padawan_v2.datasets.web_dev_prompt import PROMPT_LATEST, PROMPT_EVAL
from qstar.graders.accepts_all_grader import AcceptsAllGrader

from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.configs import (
    make_multistage_grader, 
)

ENABLE_NETWORK = True
HINT_PROBABILITY = 0.25  # Probability of including hints in the prompt

@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig, IsOverride):
    caas_cpu_limit: float = 1.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 2
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 10 * 60
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


def _make_tool_configs(
    container_tool_config: str = "padawan_tool",
    internet_policy: str = "no_access",
) -> tuple[ToolConfig, ...]:
    container_tool_config = "padawan_tool" # Force to use padawan tool for now.
    if container_tool_config == "padawan_tool":
        return (PadawanToolConfig(internet_policy=internet_policy),)
    elif container_tool_config == "mix_tool":
        return (MixToolConfig(internet_policy=internet_policy),)
    else:
        raise ValueError(f"Unknown padawan tool config: {container_tool_config}")


# Copied from project/deep_swe/deep_swe/datasets/configs.py with updated grader path and bus line.
# let's not checking tool use for now, as we only care about the final answer
def make_webdev_multistage_grader() -> MultiStageGrader:
    grader_argvs = [
        [
            "=deep_swe_msft.webdev_padawan_v2.graders.webdev_grader:WebDevCotograder",
            f"renderer_name={COTOGRADER_RENDERER}",
            *COTOGRADER_CHZ_ARGV,
            f"grader_max_tokens={16384}",
        ]
    ]
    return make_multistage_grader(
        grader_argvs,
        tuple([]),
    )  # channels_for_answer not used.

    # [TODO](xiaofewa): create mult-stage grader for webdev arena.
    # return MultiStageGrader(
    #         graders=(
    #             AcceptsAllGrader(),
    #         )
    #     )


def random_tool_set() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:random_tool_set",
    )


# Copied from project/deep_swe/deep_swe/datasets/configs.py with an updated dataset_id, tool_configs, resource_conigs, datapoint_converters
@chz.chz
class WebDevDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.train"
    grader: Grader[HarmonyCompletionDatapoint] = override(make_webdev_multistage_grader)
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            container_tool_config='padawan_tool',
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                # setup_fn=webdev_setup_fn_coreutils,
                # caas_container_image="acrbuiltincaasglobalame.azurecr.io/aio:20250703-1260143-official",
                setup_fn=webdev_setup_fn_coreutils_with_mcp_tools,
                caas_container_image="acrcommitcaassouthcentralusame.azurecr.io/actions-runner-terminal-server-webdev:20250724",
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            # [TODO](xiaofewa): use a more appropriate datapoint converter for this dataset.
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_datapoint_converter",
            ),
        )
    )


def webdev_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    system_message = chat.Message.system(
        instructions=PROMPT_LATEST,
    )

    hint_message = '''
Hints:
- You must use `npm create-react-app` and `npm start` to create a new React app
- You must take a screenshot using `playwright_mcp_server_browser_take_screenshot`
- Screenshot paths should be included in the final PR description using markdown syntax: `![screenshot](path/to/screenshot.png)`
- Playwright MCP tools default output folder is `/root/screenshots/`. This path will be used to store screenshots.
- You must attempt to use these MCP tools:
  - `playwright_mcp_server_browser_navigate`
  - `playwright_mcp_server_browser_take_screenshot`
  - `playwright_mcp_server_browser_type`
  - `playwright_mcp_server_browser_click`
'''

    # Randomly decide whether to append the screenshot message
    if random.random() < HINT_PROBABILITY:  # Use HINT_PROBABILITY to set the chance to append the screenshot message
        screenshot_message = chat.Message.user(
            content=hint_message,
        )
        new_convo = convo.with_messages(
            [
                system_message,
                *convo.messages,
                screenshot_message,
            ]
        )
    else:
        new_convo = convo.with_messages(
            [
                system_message,
                *convo.messages,
            ]
        )
    dp = {"problem": new_convo}
    return [dp]


def webdev_multiturn_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    
    # Extract messages by role
    user_messages = [msg for msg in convo.messages if msg.author.role == "user"]
    assistant_messages = [msg for msg in convo.messages if msg.author.role == "assistant"]
    
    # Get first user message for problem statement
    first_user_content = user_messages[0].content.parts[0] if user_messages[0].content.parts else ""
    
    # Get second user message for issue description  
    second_user_content = user_messages[1].content.parts[0] if user_messages[1].content.parts else ""
    
    # Get first assistant message for repository context
    first_assistant_content = assistant_messages[0].content.parts[0] if assistant_messages[0].content.parts else ""
    
    # Create structured prompt
    structured_prompt = f"""You are working on an issue in a web development repository.

<repository_context>
The repository contains the following code:

{first_assistant_content}
</repository_context>

Consider the following problem statement:
<problem_statement>

*This section details on the original issue you should resolve*
<issue_title>

{second_user_content}
</issue_title>

<issue_description>

The user initially requested: "{first_user_content}"

However, after reviewing the current repository implementation, additional requirements and modifications have been identified. The specific issues and improvements needed are detailed as follows:

{second_user_content}

These modifications are necessary to ensure the implementation fully meets the user's requirements and addresses any gaps or issues in the current codebase.
</issue_description>

</problem_statement>

Implement the necessary changes to meet the requirements specified in the problem statement and issue description."""

    hint_message = '''
Hints:
- You must use `npm create-react-app` and `npm start` to create a new React app
- You must take a screenshot using `playwright_mcp_server_browser_take_screenshot`
- Screenshot paths should be included in the final PR description using markdown syntax: `![screenshot](path/to/screenshot.png)`
- Playwright MCP tools default output folder is `/root/screenshots/`. This path will be used to store screenshots.
- You must attempt to use these MCP tools:
  - `playwright_mcp_server_browser_navigate`
  - `playwright_mcp_server_browser_take_screenshot`
  - `playwright_mcp_server_browser_type`
  - `playwright_mcp_server_browser_click`
'''

    # Create system message with the latest prompt
    system_message = chat.Message.system(
        instructions=PROMPT_LATEST,
    )
    
    # Create a new user message with the structured prompt
    structured_user_message = chat.Message.user(
        content=structured_prompt,
    )
    
    # Include any remaining messages after the first two user messages
    remaining_messages = convo.messages[4:] if len(convo.messages) > 4 else []
    
    # Randomly decide whether to append the screenshot message
    if random.random() < HINT_PROBABILITY:  # Use HINT_PROBABILITY to set the chance to append the screenshot message
        screenshot_message = chat.Message.user(
            content=hint_message,
        )
        new_convo = convo.with_messages(
            [
                system_message,
                structured_user_message,
                *remaining_messages,
                screenshot_message,
            ]
        )
    else:
        new_convo = convo.with_messages(
            [
                system_message,
                structured_user_message,
                *remaining_messages,
            ]
        )

    dp = {"problem": new_convo}
    return [dp]


def webdev_eval_datapoint_converter(dp: dict[str, Any]) -> list[dict[str, Any]]:
    convo = chat.Conversation.model_validate(dp)
    system_message = chat.Message.system(
        instructions=PROMPT_EVAL,
    )
    new_convo = convo.with_messages(
        [
            system_message,
            *convo.messages,
        ]
    )
    dp = {"problem": new_convo}
    return [dp]


# ===== WebDevArena Train datasets ======

@chz.chz
class WebDevArenaSingleTurnFilteredDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.train_v2"


@chz.chz
class WebDevArenaMultiTurnFilteredDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.multi_turn.train"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_multiturn_datapoint_converter",
            ),
        )
    )

# ===== WebDevArena Eval datasets =====

@chz.chz
class WebDevArenaSingleTurnEvalDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena.single_turn.test"

@chz.chz
class WebDevArenaSingleTurnFilteredEvalDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.test"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_eval_datapoint_converter",
            ),
        )
    )

@chz.chz
class WebDevSweagentdEvalDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena_filtered.single_turn.sweagentd"
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            FunctionWrapper(
                name="deep_swe_msft.webdev_padawan_v2.datasets.configs:webdev_eval_datapoint_converter",
            ),
        )
    )

@chz.chz
class WebDevArenaMultiTurnEvalDatasetConfig(WebDevDatasetConfig, IsOverride):
    dataset_id: str = "data.xiaofewa.appberry.react_lmsys_webdevarena.multi_turn.test"

