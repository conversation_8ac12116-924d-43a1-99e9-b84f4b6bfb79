import random
import string
import j<PERSON>
from typing import Any

import blobfile as bf
import structlog
from caas.terminal.api import TerminalSession
from caas.commands import RawExec, UploadFile, BashScript, Exec, HttpGet
from caas import ExecError
from deep_swe_msft.tools.get_coreutils import setup_coreutils
from caas_utils import hacky_pip_mitm_ca_env, setup_path
from blackberry_utils.error_types import ProblemSetupError
from deep_swe_msft.webdev_padawan_v2.datasets.mcp import start_mcp_server, send_mcp_request_to_server, test_mcp_server, create_html


logger = structlog.get_logger(component=__name__)

# Constants for webdev setup
CAAS_ARTIFACTORY_NPM_REGISTRY = "https://npm.pkg.privatelink.caas.azure.com"


async def webdev_setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:

    repo_root = f"/root"
    
    # Basic setup command - you may need to adjust this based on your requirements
    setup_cmd = "echo 'Basic setup completed'"

    try:
        output = await terminal_session.session.run(
            Exec(cmd=["bash", "-c", setup_cmd], timeout=300, env=hacky_pip_mitm_ca_env())
        )
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"{output}\n")
        logger.info("Setup script output", output=output)

    except ExecError as err:
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Error preparing setup commands: {err},\n{setup_cmd}\n")
        raise ProblemSetupError(
            f"Error in setup script (exit code {err.status}): {err.output.decode(errors='ignore')}"
        ) from err
    return repo_root


async def webdev_setup_fn_with_mcp_tools(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """Enhanced webdev setup with Node.js, Yeoman, and Playwright installation"""
    
    repo_root = f"/root"
    session = terminal_session.session
    
    try:
        # Enable network and configure npm registry
        await session.update_network(enable_network=True)
        await session.run(Exec(["bash", "-lc", f"npm config set registry {CAAS_ARTIFACTORY_NPM_REGISTRY}"]))

        logger.info("="*100)
        logger.info("Node.js and npm have been installed")
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write("Node.js and npm have been installed\n")
        # Verify Node.js and npm installation
        node_version = await session.run(Exec(["node", "--version"], timeout=30))
        npm_version = await session.run(Exec(["npm", "--version"], timeout=30))
        logger.info(f"Node.js version: {node_version.decode().strip()}")
        logger.info(f"npm version: {npm_version.decode().strip()}")
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Node.js version: {node_version.decode().strip()}\n")
            f.write(f"npm version: {npm_version.decode().strip()}\n")
        logger.info("="*100)
        logger.info("Installing Yeoman")
        
        # Install Yeoman globally
        output = await session.run(RawExec(['bash', '-c', 'npm install -g yo'], timeout=300, workdir='/root',
                                           env={"NODE_EXTRA_CA_CERTS": "/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt"}))
        logger.info(f"Install Yeoman: Exit Code: {output[0]} Output: {output[1].decode()}")
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Install Yeoman: Exit Code: {output[0]} Output: {output[1].decode()}\n")

        # Verify Yeoman installation
        yo_version = await session.run(Exec(["yo", "--version"], timeout=30))
        logger.info(f"Yeoman version: {yo_version.decode().strip()}")
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Yeoman version: {yo_version.decode().strip()}")

        # Install create-react-app globally
        output = await session.run(RawExec(['bash', '-c', 'npm install -g create-react-app'], timeout=300, workdir='/root',
                                           env={"NODE_EXTRA_CA_CERTS": "/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt"}))
        logger.info(f"Install create-react-app: Exit Code: {output[0]} Output: {output[1].decode()}")
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Install create-react-app: Exit Code: {output[0]} Output: {output[1].decode()}\n")

        # Check Playwright CLI via npx
        output = await session.run(RawExec(['bash', '-c', 'npx playwright --version'], timeout=30, workdir='/root'))
        logger.info(f"Playwright version: {output[1].decode().strip()}")
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Playwright version: {output[1].decode().strip()}\n")

        # Create the screenshots directory first
        print("\nCreating /root/screenshots directory...")
        dir_result = await session.run(RawExec([
            'bash', '-c', 'mkdir -p /root/screenshots && chmod 777 /root/screenshots && ls -la /root/ | grep screenshots && echo "Directory created successfully"'
        ], timeout=10, workdir='/root'))
        print(f"Directory creation result: {dir_result[1].decode().strip()}")
        
        logger.info("="*100)
        
    except Exception as err:
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Error in webdev setup with tools: {err}\n")
        raise RuntimeError(f"Failed to setup webdev tools: {err}") from err
    return repo_root


async def webdev_setup_fn_coreutils(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """Standard webdev setup with coreutils"""
    repo_root = await webdev_setup_fn(datapoint=datapoint, terminal_session=terminal_session)
    try:
        await setup_coreutils(terminal_session.session, {}, repo_root)
    except Exception as e:
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Failed to run ls command: {e}\n")
        raise RuntimeError(f"Failed to install coreutils: {e}") from e


async def webdev_setup_fn_coreutils_with_mcp_tools(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    """Enhanced webdev setup with coreutils, Node.js, and Playwright MCP tools"""
    repo_root = await webdev_setup_fn_with_mcp_tools(datapoint=datapoint, terminal_session=terminal_session)
    try:
        # Start the long-running MCP server
        server_result = await start_mcp_server(
            terminal_session=terminal_session,
            output_dir='/root/screenshots',
        )
        
        if "error" in server_result:
            raise RuntimeError(f"Failed to start MCP server: {server_result['error']}")
        
        logger.info(f"MCP server started successfully: {server_result}")
        
        # # Test the MCP server functionality
        # test_result = await test_mcp_server(
        #     terminal_session=terminal_session,
        #     output_dir=repo_root
        # )
        
        # if test_result.get("status") == "error":
        #     logger.warning(f"MCP server test failed: {test_result.get('error')}")
        # else:
        #     logger.info("MCP server test completed successfully")

        await setup_coreutils(terminal_session.session, {}, repo_root, skip_tool_packages=True)
    except Exception as e:
        with open("/var/log/supervisor/tools_webdev_env_setup.log", "a") as f:
            f.write(f"Failed to run ls command: {e}\n")
        raise RuntimeError(f"Failed to install coreutils: {e}") from e
