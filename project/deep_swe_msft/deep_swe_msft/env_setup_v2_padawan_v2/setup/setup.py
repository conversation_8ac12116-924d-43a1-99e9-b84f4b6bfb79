import os
import shlex
import random
from typing import Any

import structlog

import caas
from caas.commands import BashScript
from caas.terminal.api import TerminalSession
from caas_utils.utils import run_with_retries

from deep_swe_msft.tools.get_coreutils import setup_coreutils
from deep_swe_msft.tools.utils import get_strawberry_ace_token
from berry_rfs.mrfs_setup import try_run_command
from typing import Literal

import structlog
from pydantic import BaseModel

logger = structlog.get_logger(component=__name__)


class ContainerResources(BaseModel):
    cpu: float  # number of cores
    memory: int  # in MB


class EnvSetupTask(BaseModel):
    type: Literal["env_setup"] = "env_setup"
    subtype: str  # e.g. "bgsetup", "dependency_resolution"
    instance_id: str  # e.g. pandas-dev__pandas-1234
    problem_statement: str

class EnvSetupTaskMetadata(BaseModel):
    """
    Generic metadata format for Env Setup tasks.
    Will be parsed from datapoint["metadata"].
    """
    cwd: str
    docker_image: str
    resources: ContainerResources = ContainerResources(cpu=32, memory=32768)  # 32GB RAM
    limits: ContainerResources = ContainerResources(cpu=32, memory=32768)
    allow_internet: bool = False

    prompt: str = ""
    description: str = ""
    notes: str = ""
    prerunner_command: str = ""
    success_command: str = ""
    docker_command: str = "/server.py"
    
    task: EnvSetupTask | None = None  


logger = structlog.get_logger(component=__name__)

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_env_setup_train_v2/caas_env_setup_train_v2/setup.py
async def env_setup_v2_setup_fn(
    *,
    datapoint: dict[str, Any],
    terminal_session: TerminalSession,
) -> None:
    metadata = EnvSetupTaskMetadata.model_validate(datapoint["metadata"])
    try:
        terminal_session.session.start_keepalive_task(keepalive_interval=60)
        await env_setup_v2_setup_fn_internal(terminal_session=terminal_session, metadata=metadata)
    finally:
        await terminal_session.session.stop_keepalive_task()

# Modified from: .../code/torchflow-mirror/project/caas_tasks/caas_env_setup_train_v2/caas_env_setup_train_v2/setup.py
async def env_setup_v2_setup_fn_internal(
    *,
    terminal_session: TerminalSession,
    metadata: EnvSetupTaskMetadata,
) -> None:
    task = metadata.task
    assert isinstance(task, EnvSetupTask)
    await setup_coreutils(terminal_session.session, {}, repo_root=metadata.cwd)