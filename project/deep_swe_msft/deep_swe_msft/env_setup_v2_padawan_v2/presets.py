import berry.preset_utils
import deep_swe_msft.env_setup_v2_padawan_v2.dataset_config as configs

from deep_swe.datasets.channel_format import DeepSWEChannelFormat, set_channel_format
from deep_swe.datasets.config_utils import chz_path

BGDB_DATASET_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, []),
]

REPO_DATASET_CONFIGS = [
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, []),
]

DEP_DATASET_CONFIGS = [
    (configs.EnvSetupV2DepSetupDatasetConfig, []),
]

MIX_1_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgsetup_dbsetup_train_v0712_280"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_180"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_23"]),
]

MIX_2_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgsetup_dbsetup_train_v0712_280"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_180"]),
]

MIX_3_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgsetup_dbsetup_train_v0712_280"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_23"]),
]

MIX_1_REPEAT_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgsetup_dbsetup_train_v0712_4200"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_1240"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_230"]),
]

MIX_4_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_280"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_155"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_18"]),
]

MIX_4_1k_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_840"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_155"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_54"]),
]

MIX_4_2k_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_1680"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_310"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_108"]),
]

MIX_4_3k_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_2520"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_465"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_162"]),
]

MIX_4_4k_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_3360"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_620"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_216"]),
]

MIX_4_5k_CONFIGS = [
    (configs.EnvSetupV2BGDBDatasetConfig, ["dataset_id=data.chenliang1.swe.bgdb_train_v0717_4200"]),
    (configs.EnvSetupV2SBHv1RepoSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.sbhv1_repo_setup_train_v0717_775"]),
    (configs.EnvSetupV2DepSetupDatasetConfig, ["dataset_id=data.chenliang1.swe.dep_setup_train_v0717_270"]),
]


format = set_channel_format(channel_format=DeepSWEChannelFormat.TOOLBERRY_V2)

train_bgdb = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in BGDB_DATASET_CONFIGS
    ],
    format,
)

train_repo = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in REPO_DATASET_CONFIGS
    ],
    format,
)

train_dep = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in DEP_DATASET_CONFIGS
    ],
    format,
)

train_mix_1 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in MIX_1_CONFIGS
    ],
    format,
)

train_mix_2 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in MIX_2_CONFIGS
    ],
    format,
)

train_mix_3 = berry.preset_utils.compose_presets(
    *[
        berry.preset_utils.training_dataset_preset(["=" + chz_path(dataset_config), *args])
        for dataset_config, args in MIX_3_CONFIGS
    ],
    format,
)

sampling_bgdb = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=" + chz_path(configs.EnvSetupV2BGDBDatasetConfig),
            "dataset.dataset_container=orngscuscresco",
            "dataset.datapoint_converters=",  # Remove augmentations
            "dataset.override_target_samples_per_instance=1",
        ]
    ),
)

sampling_repo = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=" + chz_path(configs.EnvSetupV2SBHv1RepoSetupDatasetConfig),
            "dataset.dataset_container=orngscuscresco",
            "dataset.datapoint_converters=",  # Remove augmentations
            "dataset.override_target_samples_per_instance=1",
        ]
    ),
)

sampling_dep = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=" + chz_path(configs.EnvSetupV2DepSetupDatasetConfig),
            "dataset.dataset_container=orngscuscresco",
            "dataset.datapoint_converters=",  # Remove augmentations
            "dataset.override_target_samples_per_instance=1",
        ]
    ),
)

eval_setupbench = berry.preset_utils.compose_presets(
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=" + chz_path(configs.SetupBenchBgdbEvalDatasetConfig),
            "dataset.dataset_container=orngscuscresco",
            "dataset.datapoint_converters=",  # Remove augmentations
            "dataset.override_target_samples_per_instance=1",
        ]
    ),
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=" + chz_path(configs.SetupBenchRepoEvalDatasetConfig),
            "dataset.dataset_container=orngscuscresco",
            "dataset.datapoint_converters=",  # Remove augmentations
            "dataset.override_target_samples_per_instance=1",
        ]
    ),
    berry.preset_utils.eval_dataset_preset(
        [
            "dataset=" + chz_path(configs.SetupBenchDepEvalDatasetConfig),
            "dataset.dataset_container=orngscuscresco",
            "dataset.datapoint_converters=",  # Remove augmentations
            "dataset.override_target_samples_per_instance=1",
        ]
    ),
)