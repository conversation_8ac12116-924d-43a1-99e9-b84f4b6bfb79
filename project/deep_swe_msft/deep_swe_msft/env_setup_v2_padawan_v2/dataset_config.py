import chz
import berry
from functools import partial

import qstar.instance_completers
import qstar.instance_optimizers
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.presets.chz_utils import IsOverride, override
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from qstar.curriculums.function_wrapper import FunctionWrapper

import caas_autograding.grader as caas_graders
from berry import InstanceCompleter, InstanceOptimizer, SampleAllocator
from qstar.allocators.continuous_rewards_adaptive_sample_allocator import (
    ContinuousRewardsAdaptiveSampleAllocator,
)

from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig 
from deep_swe.datasets import configs as default_configs
from deep_swe.datasets.configs import DeepSWEDatasetConfig, conversation_converter
from deep_swe_msft.env_setup_v2_padawan_v2.setup.setup import env_setup_v2_setup_fn
from deep_swe_msft.env_setup_v2_padawan_v2.setup.caas_resource_config import EnvSetupCaasContainerResourceConfig

from deep_swe_msft.tools.caas_padawan_tool import PadawanToolConfig
from deep_swe_msft.tools.caas_mix_tool import MixToolConfig
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT_EASE, PADAWAN_SYSTEM_PROMPT, PADAWAN_SYSTEM_PROMPT_WITHOUT_REPORT_PROGRESS
from deep_swe_msft.padawan_data.sampler import PadawanSampleCompleter
from deep_swe_msft.tools.utils import CAAS_ENDPOINT

from deep_swe_msft.padawan_graders.cotograder_utils import (
    COTOGRADER_RENDERER,
    CotoGraderService
)
from deep_swe_msft.padawan_graders.cotograder_utils import COTOGRADER_RENDERER, COTOGRADER_CHZ_ARGV
from deep_swe_msft.padawan_graders.env_setup_following_grader import EnvSetupFollowCotograder

CAAS_IDLE_TTL = 1200


def make_env_setup_v2_resource_configs(
    caas_resource_config: EnvSetupCaasContainerResourceConfig,
) -> tuple[ResourceConfig, ...]:
    caas_resource_config = chz.replace(caas_resource_config, setup_fn=env_setup_v2_setup_fn)
    return (caas_resource_config,)


def make_env_setup_v2_graders(
    use_cotograder: bool = True, 
) -> tuple[Grader[HarmonyCompletionDatapoint], ...]:
    graders = [caas_graders.TermberryGrader(grade_fn="deep_swe_msft.env_setup_v2_padawan_v2.graders.unitest_grader:grade_fn_v2")]
    if use_cotograder:
        graders.append(
            EnvSetupFollowCotograder(
                grader_service=CotoGraderService(),
                renderer_name=COTOGRADER_RENDERER,
                grader_max_tokens=16384,
            )
        )
    return tuple(graders)

def _make_tool_configs(
    container_tool_config: str = "padawan_tool",
    internet_policy: str = "no_access",
) -> tuple[ToolConfig, ...]:
    if container_tool_config == "padawan_tool":
        return (PadawanToolConfig(internet_policy=internet_policy),)
    elif container_tool_config == "mix_tool":
        return (MixToolConfig(internet_policy=internet_policy),)
    else:
        raise ValueError(f"Unknown padawan tool config: {container_tool_config}")


def random_padawan_system_prompt() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:random_padawan_system_prompt",
    )

def random_tool_set() -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:random_tool_set",
    )

ENABLE_NETWORK = True

@chz.chz
class EnvSetupV2Grader(caas_graders.MultiStageGraderWithAuxReinforcements, IsOverride):
    graders: tuple[Grader[HarmonyCompletionDatapoint], ...] = override(make_env_setup_v2_graders)

def _env_setup_v2_graders_func_wrapper(
    use_cotograder: bool = False,
) -> Grader[HarmonyCompletionDatapoint]:
    return EnvSetupV2Grader(graders=make_env_setup_v2_graders(use_cotograder=use_cotograder))


@chz.chz(typecheck=True)
class EnvSetupV2BGDBDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default="orngscuscresco")
    dataset_id: str = (
        # "data.chenliang1.swe.bgdb_train_v0717_280"
        "data.chenliang1.swe.bgsetup_dbsetup_train_v0712_280"
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            _env_setup_v2_graders_func_wrapper,
            use_cotograder=True
        )
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            container_tool_config='padawan_tool',
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            EnvSetupCaasContainerResourceConfig(setup_fn=env_setup_v2_setup_fn,
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                               enable_network_after_setup=ENABLE_NETWORK),
        )
    )

    model_identity_str: str = PADAWAN_MODEL_IDENTITY
    instructions: str = PADAWAN_SYSTEM_PROMPT_WITHOUT_REPORT_PROGRESS

    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            random_tool_set(),
        )
    )

@chz.chz(typecheck=True)
class EnvSetupV2SBHv1RepoSetupDatasetConfig(EnvSetupV2BGDBDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.sbhv1_repo_setup_train_v0717_180"

@chz.chz(typecheck=True)
class EnvSetupV2DepSetupDatasetConfig(EnvSetupV2BGDBDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.dep_setup_train_v0717_23"

@chz.chz(typecheck=True)
class SetupBenchBgdbEvalDatasetConfig(EnvSetupV2BGDBDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.setupbench_v2_4_bgdb_23"
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            _env_setup_v2_graders_func_wrapper,
            use_cotograder=False
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            EnvSetupCaasContainerResourceConfig(setup_fn=env_setup_v2_setup_fn,
                                               is_eval=True,  # chenliang1: this will enable network access at caas init to avoid encountering too many errors in padawan tool installation.
                                               caas_endpoint=CAAS_ENDPOINT,
                                               caas_idle_ttl=CAAS_IDLE_TTL,
                                               enable_network_after_setup=ENABLE_NETWORK),
        )
    )

@chz.chz(typecheck=True)
class SetupBenchRepoEvalDatasetConfig(SetupBenchBgdbEvalDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.setupbench_v2_4_repo_54"

@chz.chz(typecheck=True)
class SetupBenchDepEvalDatasetConfig(SetupBenchBgdbEvalDatasetConfig, IsOverride):
    dataset_id: str = "data.chenliang1.swe.setupbench_v2_4_dep_16"
