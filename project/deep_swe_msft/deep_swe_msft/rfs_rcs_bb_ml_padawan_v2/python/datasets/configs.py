from functools import partial
from typing import Any

import structlog

import caas_autograding.grader as caas_graders
import chz
import berry
from berry.function_wrapper import FunctionWrapper
from bus.qos_type import QoSType
from bus_token_completer import BusTokenCompleter
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.mrfs_setup import mrfs_setup_fn_coreutils
from caas_swe_bench_train import SWEBenchDatapoint
from caas_swe_bench_train import setup_fn as caas_swe_bench_train_setup_fn
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe_msft.tools.caas_padawan_tool import PadawanToolConfig
from deep_swe_msft.tools.caas_mix_tool import MixToolConfig
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.caas_resource_config import CaasContainerResourceConfig
from deep_swe.datasets.setup import setup_fn_coreutils
from deep_swe.graders.thoroughness_grader import ThoroughnessGrader
from deep_swe.tools.jam_retrieval_tool import BerryJamRetrievalToolConfig  # type: ignore
from qstar.common.datapoint import Datapoint, HarmonyCompletionDatapoint
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.variant_producer import (
    VarDiscountingVariantProducer,
    VariantProducer,
)
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.graders.taskgen_utils import (
    BerryGraderConfig,
    TokenCompleterGraderService,
)
from qstar.presets.chz_utils import IsOverride, override

from token_completer import TokenCompleter

from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CRESCO_STORAGE_NAME
from deep_swe_msft.padawan_data.sampler import PadawanSampleCompleter
from qstar.samplers import BaseSampler
from deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.utils.train_constants import RFS_LANG_TO_IMAGE_NAME, ENABLE_NETWORK
from deep_swe_msft.padawan_graders.cotograder_utils import COTOGRADER_RENDERER, COTOGRADER_CHZ_ARGV

logger = structlog.stdlib.get_logger(component=__name__)

@chz.chz
class CaasResourceConfig(CaasContainerResourceConfig, IsOverride):
    caas_cpu_limit: float = 2.0  # Increase to 2 if actually using VSCode
    caas_memory_limit: int = 2
    caas_container_image: str = "vscode-agent"  # Has more pip dependencies!
    caas_idle_ttl: int = 10 * 60
    enable_network_after_setup: bool = False
    caas_endpoint: str = CAAS_ENDPOINT


@chz.chz
class DeepSWEVardiscProducer(VarDiscountingVariantProducer, IsOverride):
    # Non-standard settings designed for the low data-efficiency, high compute-efficiency regime
    reward_distribution_power: float = 1.0
    min_reward_multiplier: int = 64
    max_reward_multiplier: int = 1024
    num_reward_multipliers: int = 1


DEFAULT_PREPEND_PROBABILITY = 0.25
DEFAULT_ENFORCE_PROBABILITY = 0.9

def _make_tool_configs(
    container_tool_config: str = "padawan_tool",
    internet_policy: str = "no_access",
) -> tuple[ToolConfig, ...]:
    container_tool_config = "padawan_tool" # 'mix_tool' is not supported for rrb now due to collect commit issues.
    if container_tool_config == "padawan_tool":
        return (PadawanToolConfig(internet_policy=internet_policy),)
    elif container_tool_config == "mix_tool":
        return (MixToolConfig(internet_policy=internet_policy),)
    else:
        raise ValueError(f"Unknown padawan tool config: {container_tool_config}")


@chz.chz
class DeepSWEDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(_make_tool_configs)

    # Defaults to an empty container (with oai coreutils installed).
    # Most tasks will probably want to override this with custom setup.
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(CaasResourceConfig(setup_fn=setup_fn_coreutils),)
    )
    max_num_yields: int = 400
    variant_producer: VariantProducer | None = override(DeepSWEVardiscProducer)


def conversation_converter(func_name: str, **kwargs: dict[str, Any]) -> FunctionWrapper:
    return FunctionWrapper(
        name="caas_converters:conversation_init_fn",
        kwargs={"func": func_name, **kwargs},
    )


# IMPORTANT: must be paired with IFEnforcementGrader.
def enforce_commands(
    probability: float = DEFAULT_ENFORCE_PROBABILITY,
    extra_good_commands: tuple[str, ...] = (),
    extra_bad_commands: tuple[str, ...] = (),
) -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:enforce_commands",
        kwargs={
            "probability": probability,
            "extra_good_commands": extra_good_commands,
            "extra_bad_commands": extra_bad_commands,
        },
    )

def get_padawan_system_prompt(
) -> FunctionWrapper:
    return FunctionWrapper(
        name="deep_swe_msft.padawan_data.datapoint_converters:get_padawan_system_prompt",
    )

def make_multistage_grader(
    grader_argvs: list[list[str]] | None = None,
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
) -> caas_graders.MultiStageGraderWithAuxReinforcements:
    # NB: we can freely set |channels_for_answer| here. See NOTE [ DeepSWEDatasetConfig.channel_format ]
    if grader_argvs is None:
        grader_argvs = []
    grader_argvs = list(grader_argvs)
    
    blueprint = chz.Blueprint(caas_graders.MultiStageGraderWithAuxReinforcements)
    for ii, grader_argv in enumerate(grader_argvs):
        argv: list[str] = []
        for arg in grader_argv:
            if not arg.startswith(("=", ".")):  # allow wildcards
                arg = "." + arg
            argv.append(f"graders.{ii}{arg}")
        blueprint = blueprint.apply_from_argv(argv)
    if channels_for_answer:
        channels_for_answer_arg = "channels_for_answer=" + ",".join(
            str(c) for c in channels_for_answer
        )
        blueprint = blueprint.apply_from_argv(["..." + channels_for_answer_arg])
    return blueprint.make()


def make_rfs_multistage_grader(
    caas_container_image: str,
    caas_endpoint: str = CAAS_ENDPOINT,
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_cotograder: bool = False, 
    use_sp_hard: bool = False
) -> MultiStageGrader:
    grader_argvs = [
        [
            "=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.graders.rfs_grader:RFSGrader",
            f"caas_container_image={caas_container_image}",
            f"caas_endpoint={caas_endpoint}",
            f"collect_commit_fn=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.graders.rfs_grader:_padawan_collect_commit",
        ]
    ]
    if use_cotograder:
        grader_argvs.append([
            "=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.graders.coto_grader:PDW_RFSCriteriaCotograder",
            *COTOGRADER_CHZ_ARGV,
        ])
        grader_argvs.append([
            "=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.graders.coto_grader:PDW_RFSComparisonCotograder",
            *COTOGRADER_CHZ_ARGV,
        ])
        grader_argvs.append([
            "=deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.graders.coto_grader:PDW_RepoLongAnswerConsistencyGrader",
            f"renderer_name={COTOGRADER_RENDERER}",
            "grader_max_tokens=32768",
            "berry_grader_config.grader_reward_multiplier=64",
            "berry_grader_config.grader_top_p=0.985",
            *COTOGRADER_CHZ_ARGV,
        ])
    if use_sp_hard:
        grader_argvs.append([
            "=deep_swe_msft.padawan_graders.system_prompt_following_grader_hard:SPFollowHardCotograder",
            *COTOGRADER_CHZ_ARGV,
            f"grader_max_tokens={16384}",
        ])
    else:
        # [
        #     "=deep_swe_msft.padawan_graders.func_enforcement_grader:FuncEnforcementGrader",
        #     "target_num_str_replaces=0.01",
        #     "use_random_check=False",
        # ],
        grader_argvs.append([
            "=deep_swe_msft.padawan_graders.system_prompt_following_grader:SPFollowCotograder",
            *COTOGRADER_CHZ_ARGV,
            f"grader_max_tokens={16384}",
        ])

    return make_multistage_grader(grader_argvs, channels_for_answer)


@chz.chz
class RFSPythonDatasetConfig(DeepSWEDatasetConfig, IsOverride):
    dataset_container: str = chz.field(default=CRESCO_STORAGE_NAME)
    dataset_id: str = "data.swang.swe.u20250423.rfs_7func.train_4093"
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(make_rfs_multistage_grader, caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"])
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            CaasResourceConfig(
                setup_fn=mrfs_setup_fn_coreutils,
                caas_container_image=RFS_LANG_TO_IMAGE_NAME["py"],
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            container_tool_config='padawan_tool',
            internet_policy='enabled' if ENABLE_NETWORK else 'no_access',
        ),
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default_factory=lambda: (
            get_padawan_system_prompt(),
            # enforce_commands(probability=0.2),
            conversation_converter("deep_swe_msft.rfs_rcs_bb_ml_padawan_v2.python.datasets.conversation_init:conversation_init_fn"),
        )
    )
    model_identity_str: str = PADAWAN_MODEL_IDENTITY
    # instructions: str = PADAWAN_SYSTEM_PROMPT

    """
    NOTE: sample_completer cannot currently be overriden normally because it gets overwritten by qstar.common.defaults.
    For more context see thread:
    """
    # @chz.init_property
    # def sample_completer(self) -> berry.SampleCompleter:
    #     return PadawanSampleCompleter()

@chz.chz
class RCSPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str =  "data.swang.swe.u20250423.rcs.train_7562"

@chz.chz
class BugBountyPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.swang.swe.u20250423.bb_hard.train_1930"

# ================ RRB Hard Datasets =================

@chz.chz
class RFSHardPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str =  "data.haoranxu.swe.data.rl.rfs_threshold_150_2136_0427"

@chz.chz
class RCSHardPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str =  "data.haoranxu.swe.data.rl.rcs_threshold_150_1399_0427"

@chz.chz
class BugBountyHardPythonDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.haoranxu.swe.data.rl.bb_threshold_150_761_0427"

@chz.chz
class RRBPythonMergedDatasetConfig(RFSPythonDatasetConfig, IsOverride):
    dataset_id: str = "data.zhendongw.swe_train.data_mix_v1.rrb_py"
