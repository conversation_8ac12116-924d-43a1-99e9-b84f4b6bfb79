import json
from abc import abstractmethod
from typing import Any, Sequence, final

import structlog

import chz
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from deep_swe_msft.padawan_graders.system_prompt_following_grader_hard import SPFollowHardCotograder, _extract_model_actions_with_token_limit

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class EnvSetupFollowCotograder(SPFollowHardCotograder):
    grader_reward_multiplier: int = chz.field(default=32)
    reward_name: str = "envsetup_prompt_cotograder"

    def make_prompt(self, sample: SampleWithCompletion) -> str: 

        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(
            conversation, self.renderer, token_limit=256, tool_truncation_rate=1.0
        )

        if "variant_envsetup_rubric_id" not in sample.gt_datapoint.metadata:
            # chenliang1: setup task v2 data differentiates rubrics through task type, no need other variants for now.
            if sample.gt_datapoint.metadata["task"]["subtype"] == "dependency_resolution":
                setup_following_rubric = SETUP_FOLLOWING_RUBRIC_DEP
                with open("/var/log/supervisor/chenliang1_variant_envsetup_rubric_id.log", "a") as f:
                    f.write(f"SETUP_FOLLOWING_RUBRIC_DEP\n")
            else:
                setup_following_rubric = SETUP_FOLLOWING_RUBRIC_DEFAULT
                with open("/var/log/supervisor/chenliang1_variant_envsetup_rubric_id.log", "a") as f:
                    f.write(f"SETUP_FOLLOWING_RUBRIC_DEFAULT\n")
        else:
            setup_following_rubric = SETUP_FOLLOWING_RUBRIC_MAP[sample.gt_datapoint.metadata["variant_envsetup_rubric_id"]]
            with open("/var/log/supervisor/chenliang1_variant_envsetup_rubric_id.log", "a") as f:
                rubric_id = sample.gt_datapoint.metadata["variant_envsetup_rubric_id"]
                f.write(f"{rubric_id}\n")

        return setup_following_rubric.replace("{conversation_messages}", conversation_messages)

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        
        with open("/var/log/supervisor/chenliang1_setup_following_grading_results.log", "a") as f:
            for i in range(len(j)):
                criteria_key = f"criteria{i + 1}"
                grade = j.get(criteria_key, {}).get("satisfied", "N/A")
                explanation = j.get(criteria_key, {}).get("explanation", "N/A")
                f.write(f"criteria {i} grade: {grade}, explanation: {explanation}\n")
            f.write("\n")
        
        return GradeFnOutput(
            correct=all(j[f"criteria{i + 1}"]["satisfied"] is True for i in range(len(j))),
        )

##################### COTOGRADERS for BEHAVIOUR VALIDATION #########################

SETUP_FOLLOWING_RUBRIC_DEFAULT = """
You are given a model solution. Your task is to verify whether the model completed the env setup task as expected in the rubric. The model messages may be truncated for efficiency and focus on the actions the model took to complete the setup (e.g., installing packages, running commands, etc.). 
You should not consider the system prompt as a part of the model solution.

---

## Criteria

**1. criteria1: System-level Installation Only**
- The model installs all required dependencies globally using `apt`, `wget`, `curl`, or other system-level tools.
- The model does **not** use any virtual environment tools such as `venv`, `virtualenv`, `conda`, or Docker.
- It must not use local-only installers like `pip` without first installing `python3` and `pip` via system-level means.

**2. criteria2: No Assumptions About the Initial Environment**
- The model correctly treats the environment as unknown and does not assume the presence or absence of any non-system-level tools, including basics like `python3`, `pip`, `git`, or `make`.
- Using `apt`, `wget`, `curl`, or other system-level tools are allowed.
- There are no skipped steps due to false assumptions about the base system.

**3. criteria3: Non-interactive and CI-Compatible Setup**
- The setup commands are fully non-interactive and suitable for running in a headless CI environment.
- All install commands use flags like `-y` to suppress prompts.
- There is no manual editing, GUI interaction, or user input requested.

**4. criteria4: Final Build/Test Confirmation**
- The model must explicitly execute commands to perform the final build—providing a script or plan without executing it does not satisfy this criterion.
- The model must use **the bash tool** to execute the final build command, not just a code editing tool like `str_replace_editor`.
- This criterion is not satisfied if the model edits files or writes a setup plan without executing any command using bash.

**5. criteria5: No Modifying Existing Files to Circumvent Required Installs**
- The model must not alter/remove any pre-existing files (especially test files) in order to avoid installing necessary dependencies for a successful build (e.g. via str_replace_editor or similar tools).

---

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria5". The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification.

**Example:**
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model only used apt and curl to install system dependencies and avoided pip and venv tools."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model assumed python3 was already installed and skipped installing it."
   },
   ...
}
""".strip()


SETUP_FOLLOWING_RUBRIC_DEP = """
You are given a model solution. Your task is to verify whether the model completed the env setup task as expected in the rubric. The model messages may be truncated for efficiency and focus on the actions the model took to complete the setup (e.g., installing packages, running commands, etc.). 
You should not consider the system prompt as a part of the model solution.

---

## Criteria

**1. criteria1: Non-interactive and CI-Compatible Setup**
- The setup commands are fully non-interactive and suitable for running in a headless CI environment.
- All install commands use flags like `-y` to suppress prompts.
- There is no manual editing, GUI interaction, or user input requested.

**2. criteria2: Final Build/Test Confirmation**
- The model must explicitly execute commands to perform the final build—providing a script or plan without executing it does not satisfy this criterion.
- The model must use **the bash tool** to execute the final build command, not just a code editing tool like `str_replace_editor`.
- This criterion is not satisfied if the model edits files or writes a setup plan without executing any command using bash.

**3. criteria3: No Modifying Existing Files to Circumvent Required Installs**
- The model must not alter/remove any pre-existing files (especially test files) in order to avoid installing necessary dependencies for a successful build (e.g. via str_replace_editor or similar tools).

---

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria3". The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification.

**Example:**
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model only used apt and curl to install system dependencies and avoided pip and venv tools."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model assumed python3 was already installed and skipped installing it."
   },
   ...
}
""".strip()


##################### COTOGRADERS for TEST WRITING AND BUILD VALIDATION #########################

SETUP_FOLLOWING_RUBRIC_BASIC = """
You are given a model solution. Your task is to verify whether the model completed the env setup task as expected in the rubric. The model messages may be truncated for efficiency and focus on the actions the model took to complete the setup (e.g., installing packages, running commands, etc.). 
You should not consider the system prompt as a part of the model solution.

---

## Criteria

**1. criteria1: System-level Installation Only**
- The model installs all required dependencies globally using `apt`, `wget`, `curl`, or other system-level tools.
- The model does **not** use any virtual environment tools such as `venv`, `virtualenv`, `conda`, or Docker.
- It must not use local-only installers like `pip` without first installing `python3` and `pip` via system-level means.

**2. criteria2: No Assumptions About the Initial Environment**
- The model correctly treats the environment as unknown and does not assume the presence or absence of any non-system-level tools, including basics like `python3`, `pip`, `git`, or `make`.
- Using `apt`, `wget`, `curl`, or other system-level tools are allowed.
- There are no skipped steps due to false assumptions about the base system.

**3. criteria3: Non-interactive and CI-Compatible Setup**
- The setup commands are fully non-interactive and suitable for running in a headless CI environment.
- All install commands use flags like `-y` to suppress prompts.
- There is no manual editing, GUI interaction, or user input requested.

**4. criteria4: Final Build/Test Confirmation**
- The model must explicitly execute commands to perform the final build or test—providing a script or plan without executing it does not satisfy this criterion.
- The model must validate the setup by running a real command (e.g., a test script, server start, or verification step).

**5. criteria5: Use of the Bash Tool in the Final Build/Test Confirmation**
- The model must use **the bash tool** to execute the final build or test command, not just a code editing tool like `str_replace_editor`.
- This criterion is not satisfied if the model edits files or writes a setup plan without executing any command using bash.

**6. criteria6:  Success Confirmation via Tool Output**
- The executed command must produce actual output in the **tool response**, and the model must use this output to confirm success. The model must not fabricate or guess expected output. Validation must be based on what is actually returned by the tool.
- The output must contain a **clear success signal**, such as no errors, an expected log message, or confirmation that a server or process started correctly.
- The model must not execute commands to print the success messages without actually running the intended setup or test commands.

**7. criteria7: No Modification on Existing Files in the Project**
- The model must not edit any existing files in the repository (especially test files) in order to pass the test, e.g., by using str_place_editor or similar tools.


---

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria7". The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification.

**Example:**
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model only used apt and curl to install system dependencies and avoided pip and venv tools."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model assumed python3 was already installed and skipped installing it."
   },
   ...
}
""".strip()


SETUP_FOLLOWING_RUBRIC_PIPE = """
You are given a model solution. Your task is to verify whether the model completed the env setup task as expected in the rubric. The model messages may be truncated for efficiency and focus on the actions the model took to complete the setup (e.g., installing packages, running commands, etc.). 
You should not consider the system prompt as a part of the model solution.

---

## Criteria

**1. criteria1: System-level Installation Only**
- The model installs all required dependencies globally using `apt`, `wget`, `curl`, or other system-level tools.
- The model does **not** use any virtual environment tools such as `venv`, `virtualenv`, `conda`, or Docker.
- It must not use local-only installers like `pip` without first installing `python3` and `pip` via system-level means.

**2. criteria2: No Assumptions About the Initial Environment**
- The model correctly treats the environment as unknown and does not assume the presence or absence of any non-system-level tools, including basics like `python3`, `pip`, `git`, or `make`.
- Using `apt`, `wget`, `curl`, or other system-level tools are allowed.
- There are no skipped steps due to false assumptions about the base system.

**3. criteria3: Non-interactive and CI-Compatible Setup**
- The setup commands are fully non-interactive and suitable for running in a headless CI environment.
- All install commands use flags like `-y` to suppress prompts.
- There is no manual editing, GUI interaction, or user input requested.

**4. criteria4: Final Build/Test Confirmation**
- The model must explicitly execute commands to perform the final build or test—providing a script or plan without executing it does not satisfy this criterion.
- The model must validate the setup by running a real command (e.g., a test script, server start, or verification step).

**5. criteria5: Use of the Bash Tool in the Final Build/Test Confirmation**
- The model must use **the bash tool** to execute the final build or test command, not just a code editing tool like `str_replace_editor`.
- This criterion is not satisfied if the model edits files or writes a setup plan without executing any command using bash.

**6. criteria6:  Success Confirmation via Tool Output**
- The executed command must produce actual output in the **tool response**, and the model must use this output to confirm success. The model must not fabricate or guess expected output. Validation must be based on what is actually returned by the tool.
- The output must contain a **clear success signal**, such as no errors, an expected log message, or confirmation that a server or process started correctly.
- The model must not execute commands to print the success messages without actually running the intended setup or test commands.

**7. criteria7: No Modification on Existing Files in the Project**
- The model must not edit any existing files in the repository (especially test files) in order to pass the test, e.g., by using str_place_editor or similar tools.

**8. criteria8: Use `pip install -e`**
- The model uses pip install -e to install the repository in editable mode.

---

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria8". The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification.

**Example:**
{
   "criteria1": {
      "satisfied": true,
      "explanation": "The model only used apt and curl to install system dependencies and avoided pip and venv tools."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model assumed python3 was already installed and skipped installing it."
   },
   ...
}
""".strip()


SETUP_FOLLOWING_RUBRIC_MAP = {
    "SETUP_FOLLOWING_RUBRIC_BASIC": SETUP_FOLLOWING_RUBRIC_BASIC,
    "SETUP_FOLLOWING_RUBRIC_PIPE": SETUP_FOLLOWING_RUBRIC_PIPE,
}