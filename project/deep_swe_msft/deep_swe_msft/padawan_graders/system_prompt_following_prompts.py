# Criteria definitions
CRITERIA_DEFINITIONS = {
    "dependency": {
        "title": "Dependency",
        "description": """
    - If missing dependencies, the model should at least try to install them, for example `pip install pytest` or `npm install <package>`. 
    - If there are no missing dependencies from tool responses, the solution should not install anything.
    - Most of the cases, the environment is already set up, so don't be too strict on this one."""
    },
    "dependency_extra": {
        "title": "Dependency",
        "description": """
    - If missing dependencies, the model should at least try to install them, for example `pip install pytest`, `npm install <package>`, `go get`, `mvn compile`, or `gradle build`. 
    - If there are no missing dependencies from tool responses, the solution should not install anything.
    - Most of the cases, the environment is already set up, so don't be too strict on this one."""
    },
    "report_progress": {
        "title": "Report Progress",
        "description": """
    - The solution regularly calls <functions.report_progress> to report its progress. 3 times is a good number in general with one at the beginning, one in the middle and one in the end, but it can be more on longer rollouts or less on shorter ones. 
    - At least one call at the beginning and one call at the end to <functions.report_progress> should be made.
    - The model should never report progress consecutively, i.e., it should not call <functions.report_progress> twice in a row without any other action in between.
    - The first call to <functions.report_progress> should contain incomplete items in a checklist in a markdown format.
    - The last call to <functions.report_progress> should only contain completed items in the checklist in a markdown format, and should not contain any pending items.
    - Be strict on this one."""
    },
    "pr_description": {
        "title": "PR Description",
        "description": """
    - At the end of the fix, the model should write a PR description for the changes made to fix the issue in format: <pr_title> ... </pr_title> <pr_description> ... </pr_description>.
    - PR description should not appear in report progress, and should be in pure text and the last message in the conversation.
    - The format check is strict, so the model should follow it exactly."""
    },
    "linting_testing": {
        "title": "Linting, building and testing",
        "description": """
    - The solution should try to lint, build and test the code according to the system prompt.
    - The model must try to run the tests in bash tool near the end, even if they fail. Be strict on this one."""
    },
    "code_style": {
        "title": "Code Style",
        "description": """
    - The solution should follow the code style guidelines specified in the system prompt."""
    },
    "code_style_run_tests": {
        "title": "Code Style",
        "description": """
    - The solution should follow the code style guidelines specified in the system prompt.
    - The model must try to run the tests in bash tool near the end, even if they fail. Be strict on this one."""
    },
    "plan": {
        "title": "Plan",
        "description": """
    The model better have a plan at the beginning before solving the task. Not strictly required, but highly recommended.
        """
    },
    "setup_and_build": {
        "title": "Setup and Build",
        "description": """
    The model should setup and build the environment before running the tests. Strictly right after the plan. (workflow order matters)
        - The model should install dependencies, and build the environment, like `npm install`, `pip install -r requirements.txt`, `go mod tidy`, `mvn compile`, `gradle build`, etc.
        - For nodejs/typescript projects, the model should run both `npm install` and `npm run build` to try setup; `npm install` must precede `npm run build`.
        - The model should run the build command in bash tool, not just quote the command in the message. `pytest -q` is not a build command.
        - The model should not run the tests before the build, as the tests may fail due to missing dependencies or build errors.
        - Running tests are not considered as building the environment; the model must run real build commands.
        """
    },
    "reproduce_issue": {
        "title": "Reproduce Issue",
        "description": """
    The model should reproduce the issue before attempting a fix. Strictly right after the build. (workflow order matters)
        - The model should write/modify test files and then run test files for reproduction instead of testing in command line, which is not good for debugging.
        - If test suite exists, only modify existing tests to cover your changes. Never create new tests if there is an existing test suite.
        - If no test suites are available, created new test files to reproduce the problem.
        - The model needs to use bash tool to run the tests, not just quote the command in the message.
        """
    },
    "fix_issue_after_repro": {
        "title": "Fix Issue",
        "description": """
    The model should fix the issue completely. Strictly right after the reproduction. (workflow order matters)
        - The model should fix the issue completely, not just partially.
        """
    },
    "fix_issue_after_plan": {
        "title": "Fix Issue",
        "description": """
    The model should fix the issue completely. Strictly after the plan. (workflow order matters)
        - The model should fix the issue completely, not just partially.
        """
    },
    "run_tests": {
        "title": "Run Tests",
        "description": """
    The model should run the tests after the fix and verify that the solution passes the tests.
        - The model should run the tests after the fix and verify that the solution passes the tests.
        - The model should try to build after the fix to make sure the code is still buildable.
        """
    }
}


RUBRIC_CONFIGS = {
    "SP_FOLLOWING_RUBRIC_EASY": ["plan", "setup_and_build", "reproduce_issue", "fix_issue_after_repro", "run_tests", "report_progress", "pr_description"],
    "SP_FOLLOWING_RUBRIC_HARD": ["plan", "fix_issue_after_plan", "report_progress", "pr_description"],
}


def _build_rubric(rubric_id: str) -> str:
    """Build a rubric programmatically based on configuration and flags"""
    criteria_list = RUBRIC_CONFIGS[rubric_id].copy()

    # Build criteria section
    criteria_section = "## Criteria\n\n"
    for i, criteria_key in enumerate(criteria_list, 1):
        criteria_def = CRITERIA_DEFINITIONS[criteria_key]
        criteria_section += f"{i}. criteria{i}: **{criteria_def['title']}**{criteria_def['description']}\n\n"
    
    # Build JSON example
    json_example = "{\n"
    example_criteria_num = 1
    
    json_example += f'   "criteria{example_criteria_num}": {{\n     "satisfied": false,\n     "explanation": "The model run its own tests."\n   }}'
    json_example += "\n   ...\n}"
    
    # Build full rubric
    rubric = f"""
You are given a model solution. Your task is to verify whether model completed the task as expected in the rubric. The model messages may be truncated for efficiency and focus on the action that the model took to complete the task, e.g., function calls. 
You should not consider the system prompt as a part of the model solution.

{criteria_section}§ Model Solution:
```
{{conversation_messages}}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria{len(criteria_list)}". Example is shown below. 
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{json_example}"""
    
    return rubric.strip()
