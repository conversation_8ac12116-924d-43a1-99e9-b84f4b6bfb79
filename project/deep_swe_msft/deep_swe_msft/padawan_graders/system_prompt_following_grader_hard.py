import json
from abc import abstractmethod
from typing import Any, Sequence, final

import structlog

import chz
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from caas_swe_bench_train_v2.cotograders import _JsonCotograder

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class SPFollowHardCotograder(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=32)
    reward_name: str = "system_prompt_cotograder"

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(conversation, self.renderer, token_limit=100)

        sp_following_rubric = SP_FOLLOWING_RUBRIC_MAP[sample.gt_datapoint.metadata["variant_sp_rubric_id"]]
        return sp_following_rubric.replace("{conversation_messages}", conversation_messages)

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        return GradeFnOutput(
            correct=all(j[f"criteria{i + 1}"]["satisfied"] is True for i in range(3))
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


def _truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"

def _extract_model_actions_with_token_limit(convo, renderer=None, token_limit: int = 512, tool_truncation_rate: float = 0.5):
    """Extract model actions with token-based truncation"""
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            cur_token_limit = token_limit
        else:
            cur_token_limit = int(token_limit * tool_truncation_rate)
        
        content_str = str(message.content)
        
        # If renderer available, use token-based truncation
        if renderer:
            truncated_content  = _truncate_string(renderer, content_str, token_limit=cur_token_limit)
        else:
            # Fallback to character-based truncation
            truncated_content = content_str[:cur_token_limit]

        convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {truncated_content}\n"
    
    return convo_messages

def _extract_model_actions(convo):
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {str(message.content)[:512]}\n"
        # else:
        #     convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {str(message.content)[:512]}\n"

    return convo_messages


SP_FOLLOWING_RUBRIC_1 = """
You are given a model solution. Your task is to verify whether model completed the task as expected in the rubric. The model messages may be truncated for efficiency and focus on the action that the model took to complete the task, e.g., function calls. 
You should not consider the system prompt as a part of the model solution.

## Criteria

1. criteria1: **Dependency**
   - If missing dependencies, the model should at least try to install them, for example `pip install pytest` or `npm install <package>`. 
   - If there are no missing dependencies from tool responses, the solution should not install anything.
   - Most of the cases, the environment is already set up, so don't be too strict on this one.

2. criteria2: **Report Progress**
    - The solution regularly calls <functions.report_progress> to report its progress. 3 times is a good number in general with one at the beginning, one in the middle and one in the end, but it can be more on longer rollouts or less on shorter ones. 
    - At least one call at the beginning and one call at the end to <functions.report_progress> should be made.
    - Be strict on this one.

3. criteria3: **PR Description**
   - At the end of the fix, the model should write a PR description for the changes made to fix the issue in format: <pr_title> ... </pr_title> <pr_description> ... </pr_description>.
   - The format check is strict, so the model should follow it exactly.

4. criteria4: **Linting, building and testing**
   - The solution should try to lint, build and test the code according to the system prompt.

5. criteria5: **Code Style**
    - The solution should follow the code style guidelines specified in the system prompt.

6. criteria6: **Function Call Format**
    - The model should never call unsupported commands in <functions.str_replace_editor>. str_replace_editor only supports the following fields in "command" argument: ["view", "create", "str_replace", "insert", "undo_edit"], for example {"command":"grep ...","path":"src"} is not a valid command.
    - The model should never include 'bash' in the `command` argument in <functions.bash>.
    - The model should never have argument errors in function calls, e.g., missing required arguments or using unsupported arguments.
    - Be strict on this one.

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria5". Example is shown below. 
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{
   "criteria1": {
      "satisfied": true,
      "explanation": "the model install dependencies correctly or no dependencies need to be installed."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model run its own tests."
   }
   ...
}
""".strip()

SP_FOLLOWING_RUBRIC_2 = """
You are given a model solution. Your task is to verify whether model completed the task as expected in the rubric. The model messages may be truncated for efficiency and focus on the action that the model took to complete the task, e.g., function calls. 
You should not consider the system prompt as a part of the model solution.

## Criteria

1. criteria1: **Dependency**
   - If missing dependencies, the model should at least try to install them, for example `pip install pytest` or `npm install <package>`. 
   - If there are no missing dependencies from tool responses, the solution should not install anything.
   - Most of the cases, the environment is already set up, so don't be too strict on this one.

2. criteria2: **Report Progress**
    - The solution regularly calls <functions.report_progress> to report its progress. 3 times is a good number in general with one at the beginning, one in the middle and one in the end, but it can be more on longer rollouts or less on shorter ones. 
    - At least one call at the beginning and one call at the end to <functions.report_progress> should be made.
    - Be strict on this one.

3. criteria3: **PR Description**
   - At the end of the fix, the model should write a PR description for the changes made to fix the issue in format: <pr_title> ... </pr_title> <pr_description> ... </pr_description>.
   - The format check is strict, so the model should follow it exactly.

4. criteria4: **Code Style**
    - The solution should follow the code style guidelines specified in the system prompt.

5. criteria5: **Function Call Format**
    - The model should never call unsupported commands in <functions.str_replace_editor>. str_replace_editor only supports the following fields in "command" argument: ["view", "create", "str_replace", "insert", "undo_edit"], for example {"command":"grep ...","path":"src"} is not a valid command.
    - The model should never include 'bash' in the `command` argument in <functions.bash>.
    - The model should never have argument errors in function calls, e.g., missing required arguments or using unsupported arguments.
    - Be strict on this one.

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria4". Example is shown below. 
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{
   "criteria1": {
      "satisfied": true,
      "explanation": "the model install dependencies correctly or no dependencies need to be installed."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model run its own tests."
   }
   ...
}
""".strip()

SP_FOLLOWING_RUBRIC_3 = """
You are given a model solution. Your task is to verify whether model completed the task as expected in the rubric. The model messages may be truncated for efficiency and focus on the action that the model took to complete the task, e.g., function calls. 
You should not consider the system prompt as a part of the model solution.

## Criteria

1. criteria1: **Dependency**
   - If missing dependencies, the model should at least try to install them, for example `pip install pytest` or `npm install <package>`. 
   - If there are no missing dependencies from tool responses, the solution should not install anything.
   - Most of the cases, the environment is already set up, so don't be too strict on this one.

2. criteria2: **Report Progress**
    - The solution regularly calls <functions.report_progress> to report its progress. 3 times is a good number in general with one at the beginning, one in the middle and one in the end, but it can be more on longer rollouts or less on shorter ones. 
    - At least one call at the beginning and one call at the end to <functions.report_progress> should be made.
    - Be strict on this one.

3. criteria3: **PR Description**
   - At the end of the fix, the model should write a PR description for the changes made to fix the issue in format: <pr_title> ... </pr_title> <pr_description> ... </pr_description>.
   - The format check is strict, so the model should follow it exactly.

4. criteria4: **Linting, building and testing**
   - The solution should try to lint, build and test the code according to the system prompt.

5. criteria5: **Function Call Format**
    - The model should never call unsupported commands in <functions.str_replace_editor>. str_replace_editor only supports the following fields in "command" argument: ["view", "create", "str_replace", "insert", "undo_edit"], for example {"command":"grep ...","path":"src"} is not a valid command.
    - The model should never include 'bash' in the `command` argument in <functions.bash>.
    - The model should never have argument errors in function calls, e.g., missing required arguments or using unsupported arguments.
    - Be strict on this one.
   
§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria4". Example is shown below. 
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{
   "criteria1": {
      "satisfied": true,
      "explanation": "the model install dependencies correctly or no dependencies need to be installed."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model run its own tests."
   }
   ...
}
""".strip()


SP_FOLLOWING_RUBRIC_MAP = {
    "SP_FOLLOWING_RUBRIC_1": SP_FOLLOWING_RUBRIC_1,
    "SP_FOLLOWING_RUBRIC_2": SP_FOLLOWING_RUBRIC_2,
    "SP_FOLLOWING_RUBRIC_3": SP_FOLLOWING_RUBRIC_3,
}
