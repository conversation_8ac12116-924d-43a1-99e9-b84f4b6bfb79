from datetime import datetime
import json
from abc import abstractmethod
from dataclasses import asdict
from typing import Any, Sequence, final

import structlog

import chz
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from caas_swe_bench_train_v2.cotograders import _JsonCotograder
from deep_swe_msft.padawan_graders.system_prompt_following_prompts import _build_rubric
from prbot_msft.configs.utils import include_install_for_easy_langs

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)


def log_with_timestamp(message: str):
    with open(f"/var/log/supervisor/deepswe_system_prompt_following_grader.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}]: {message}\n")


@chz.chz(typecheck=True)
class SWEWorkFlowCotograder(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=32)
    reward_name: str = "swe_workflow_cotograder"

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(conversation, self.renderer, token_limit=256)

        return SWE_WF_RUBRIC.replace("{conversation_messages}", conversation_messages)

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        log_with_timestamp(f"Grading JSON: {json.dumps(j, indent=2)}")
        return GradeFnOutput(
            correct=all(j[f"criteria{i + 1}"]["satisfied"] is True for i in range(3))
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


def _truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"

def _extract_model_actions_with_token_limit(convo, renderer=None, token_limit: int = 512):
    """Extract model actions with token-based truncation"""
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            content_str = str(message.content)
            
            # If renderer available, use token-based truncation
            if renderer:
                truncated_content  = _truncate_string(renderer, content_str)
            else:
                # Fallback to character-based truncation
                truncated_content = content_str[:token_limit]
                
            convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {truncated_content}\n"
    
    return convo_messages


SWE_WF_RUBRIC = """
You are given a model solution. Your task is to verify whether model completed the task as expected in the rubric. The model messages may be truncated for efficiency and focus on the action that the model took to complete the task, e.g., function calls. 
You should not consider the system prompt as a part of the model solution.

## Criteria

1. criteria1: **SWE Work Flow**
    The is a critical critera. The following order of steps is strictly required for the model to follow when solving the task:
    - 1. The model better have a plan at the beginning before solving the task. Not strictly required, but highly recommended.
    - 2. The model should setup and build the environment before running the tests. Strictly right after the plan. (workflow order matters)
        - The model should install dependencies, and build the environment, like `npm install`, `pip install -r requirements.txt`, `go mod tidy`, `mvn compile`, `gradle build`, etc.
        - For nodejs/typescript projects, the model should run both `npm install` and `npm run build` to try setup; `npm install` must precede `npm run build`.
        - The model should run the build command in bash tool, not just quote the command in the message. `pytest -q` is not a build command.
        - The model should not run the tests before the build, as the tests may fail due to missing dependencies or build errors.
        - Running tests are not considered as building the environment; the model must run real build commands.
    - 3. The model should reproduce the issue before attempting a fix. Strictly right after the build. (workflow order matters)
        - The model should write/modify test files and then run test files for reproduction instead of testing in command line, which is not good for debugging.
        - If test suite exists, only modify existing tests to cover your changes. Never create new tests if there is an existing test suite.
        - If no test suites are available, created new test files to reproduce the problem.
        - The model needs to use bash tool to run the tests, not just quote the command in the message.
    - 4. The model should fix the issue completely and run the tests after the fix. Strictly right after the reproduction. (workflow order matters)
        - The model should fix the issue completely, not just partially.
        - The model should run the tests after the fix and verify that the solution passes the tests.
        - The model should try to build after the fix to make sure the code is still buildable.

2. criteria2: **Report Progress**
    - The solution regularly calls <functions.report_progress> to report its progress. 3 times is a good number in general with one at the beginning, one in the middle and one in the end, but it can be more on longer rollouts or less on shorter ones. 
    - At least one call at the beginning and one call at the end to <functions.report_progress> should be made.
    - The model should never report progress consecutively, i.e., it should not call <functions.report_progress> twice in a row without any other action in between.
    - The first call to <functions.report_progress> should contain incomplete items in a checklist in a markdown format.
    - The last call to <functions.report_progress> should only contain completed items in the checklist in a markdown format, and should not contain any pending items.
    - Be strict on this one.

3. criteria3: **PR Description**
    - At the end of the fix, the model should write a PR description for the changes made to fix the issue in format: <pr_title> ... </pr_title> <pr_description> ... </pr_description>.
    - PR description should not appear in report progress, and should be in pure text and the last message in the conversation.
    - The format check is strict, so the model should follow it exactly.

§ Model Solution:
```
{conversation_messages}
```

---

Your final # Answer should be in JSON. You must list all criterias as "criteria1" to "criteria3". Example is shown below. 
The "satisfied" field should be a boolean indicating whether the criterion is met, and the "explanation" field should provide a brief justification. 
Example:
{
   "criteria1": {
      "satisfied": true,
      "explanation": "the model follows the SWE workflow by first creating a plan, then setting up the environment ..."
   },
   "criteria2": {
     "satisfied": false,
     "explanation": "The model did not report progress three times."
   }
   ...
}
"""