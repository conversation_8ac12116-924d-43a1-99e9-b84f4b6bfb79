from datetime import datetime
import json
from abc import abstractmethod
from dataclasses import asdict
from typing import Any, Sequence, final

import structlog

import chz
from chat.render.common import render_content
from qstar.common import types
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    GradeFnOutput,
)
from caas_swe_bench_train_v2.cotograders import _JsonCotograder
from deep_swe_msft.padawan_graders.system_prompt_following_prompts import _build_rubric
from prbot_msft.configs.utils import include_install_for_easy_langs

SampleWithCompletion = types.SampleWithCompletion[HarmonyCompletionDatapoint]

logger = structlog.stdlib.get_logger(component=__name__)


def log_with_timestamp(message: str):
    with open(f"/var/log/supervisor/deepswe_system_prompt_following_grader.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}]: {message}\n")


@chz.chz(typecheck=True)
class SPFollowCotograder(_JsonCotograder):
    grader_reward_multiplier: int = chz.field(default=32)
    reward_name: str = "system_prompt_cotograder"

    def make_prompt(self, sample: SampleWithCompletion) -> str:
        conversation = sample.conversation
        assert conversation is not None, "Conversation is required for this task"
        conversation_messages = _extract_model_actions_with_token_limit(conversation, self.renderer, token_limit=256)

        rubric_id = sample.gt_datapoint.metadata["variant_sp_rubric_id"]
        instance_id = sample.gt_datapoint.metadata.get("instance_id", "unknown_instance")
        log_with_timestamp(f"[{instance_id}] Making prompt for rubric_id: {rubric_id}")

        sp_following_rubric = _build_rubric(
            rubric_id=rubric_id,
        )
        return sp_following_rubric.replace("{conversation_messages}", conversation_messages)

    @classmethod
    def grade_json(cls, j: dict[str, Any]) -> GradeFnOutput:
        log_with_timestamp(f"Grading JSON: {json.dumps(j, indent=2)}")
        return GradeFnOutput(
            correct=all(j[f"criteria{i + 1}"]["satisfied"] is True for i in range(3))
        )

    @classmethod
    def reward(cls, grade: dict[str, Any]) -> float:
        return int(grade["correct"])

    @classmethod
    def is_correct(cls, grade: dict[str, Any]) -> bool:
        return grade["correct"]


def _truncate_string(
    renderer,
    string: str,
    token_limit: int = 512,
    truncate_behavior: str = "middle",
) -> str:
    toks = renderer.encode(string)
    if len(toks) < token_limit:
        return string

    if truncate_behavior == "middle":
        return (
            renderer.decode(toks[: token_limit // 2])
            + "...(truncated)..."
            + renderer.decode(toks[-token_limit // 2 :])
        )

    return renderer.decode(toks[:token_limit]) + "...(truncated)"

def _extract_model_actions_with_token_limit(convo, renderer=None, token_limit: int = 512):
    """Extract model actions with token-based truncation"""
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            content_str = str(message.content)
            
            # If renderer available, use token-based truncation
            if renderer:
                truncated_content  = _truncate_string(renderer, content_str)
            else:
                # Fallback to character-based truncation
                truncated_content = content_str[:token_limit]
                
            convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {truncated_content}\n"
    
    return convo_messages

def _extract_model_actions(convo):
    convo_messages = ''
    for message in convo.messages:
        if message.author.role != 'tool':
            convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {str(message.content)[:512]}\n"
        # else:
        #     convo_messages += f"<author:{message.author.role}> -> <recipient:{message.recipient}>:  {str(message.content)[:512]}\n"

    return convo_messages
