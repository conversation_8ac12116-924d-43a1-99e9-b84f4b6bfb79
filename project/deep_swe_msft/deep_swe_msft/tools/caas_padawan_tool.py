import functools
import json
import shlex
from typing import Any, AsyncIterator, Collection, Literal

import structlog
import orjson
import chat
import chz
from caas_tool.caas_container import <PERSON>aasContainer
from chat.render.common import render_content
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from berry_harmony.tools.berry_tool_interface import Berry<PERSON>ool
from berry_caas_container_tool.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from berry_caas_container_tool.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
    _caas_container_resource_name
)
from berry_caas_container_tool.caas_container_tool import (
    CaasToolPluginConfig,
    create_caas_tool_plugins,
)
from deep_swe_msft.tools.utils import PADAWAN_TOOL_ERROR_MSGS

from abc import abstractmethod
from typing import Annotated, Async<PERSON>tera<PERSON>, ClassVar, Iterator, Sequence, Literal, Callable
from function_calling_tool import FunctionCallingTool, function_the_model_can_call

from deep_swe_msft.padawan_data.datapoint_converters import ENFORCE_COMMANDS_FORBIDDEN

logger = structlog.stdlib.get_logger(component=__name__)

TOOL_MAXTOKENS = 8192
STR_EDITOR_ALLOWED_COMMANDS = ["view", "create", "str_replace", "insert", "undo_edit"]

def _process_tool_message(message: str | chat.Message) -> str:
    """
    Truncate the message to a maximum of TOOL_MAXTOKENS tokens.
    """
    if isinstance(message, str):
        if len(message) > TOOL_MAXTOKENS:
            # Truncate in the middle, keeping beginning and end
            truncate_marker = " <truncated...> "
            available_space = TOOL_MAXTOKENS - len(truncate_marker)
            start_length = available_space // 2
            end_length = available_space - start_length
            message = message[:start_length] + truncate_marker + message[-end_length:]
    else:
        if len(message.content.parts[0]) > TOOL_MAXTOKENS:
            # Truncate in the middle, keeping beginning and end
            truncate_marker = " <truncated...> "
            available_space = TOOL_MAXTOKENS - len(truncate_marker)
            start_length = available_space // 2
            end_length = available_space - start_length
            message.content.parts[0] = (message.content.parts[0][:start_length] + 
                                      truncate_marker + 
                                      message.content.parts[0][-end_length:])
    return message

class DeepSWECaasPadawanTool(OriginalBerryCaasContainerTool):
    """
    Applies tool penalties and (forbidden) command enforcement!

    NOTE: Required command enforcement is done in IFEnforcementGrader;
    however failing forbidden commands immediately is more efficient and is a sort of weak credit assignment.
    """

    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 10_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = 'default',
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set

    # Remove feed_chars from the tool instructions
    @classmethod
    @functools.cache
    def get_names_of_functions_the_model_can_call(cls) -> Collection[str]:
        result = set(super().get_names_of_functions_the_model_can_call())
        result.discard("feed_chars")
        result.discard("exec")
        return result
    
    @classmethod
    def get_tool_name(cls) -> str:
        return "functions"
    
    @classmethod
    def get_description(cls) -> str | None:
        return 'Microsoft Padawan tool for code generation and editing'
    
    @function_the_model_can_call
    async def str_replace_editor(
        self,
        command: Annotated[
            Literal["view", "create", "str_replace", "insert", "undo_edit"],  
            "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`."
        ],
        path: Annotated[
            str,  
            "Absolute path to file or directory."
        ],
        file_text: Annotated[
            str | None,  
            "Required parameter of `create` command, with the content of the file to be created."
        ] = None,
        insert_line: Annotated[
            int | None,  
            "Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`."
        ] = None,
        new_str: Annotated[
            str | None,  
            "Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert."
        ] = None,
        old_str: Annotated[
            str | None,  
            "Required parameter of `str_replace` command containing the string in `path` to replace. Leading and ending whitespaces from file content should be preserved!"
        ] = None,
        view_range: Annotated[
            list[int] | None,  
            "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Custom editing tool for viewing, creating and editing files
        * State is persistent across command calls and discussions with the user
        * If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep
        * The `create` command cannot be used if the specified `path` already exists as a file
        * If a `command` generates a long output, it will be clipped in the middle and marked with `<file too long...`
        * The `undo_edit` command will revert the last edit made to the file at `path`
        Notes for using the `str_replace` command:
        * The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file.
        * If the `old_str` parameter is not unique in the file, the replacement will not be performed. Make sure to include enough context in `old_str` to make it unique
        * The `new_str` parameter should contain the edited lines that should replace the `old_str`
        """
        cmd = {"command": command, "path": path}
        
        if view_range is not None:
            cmd["view_range"] = view_range
            
        if file_text is not None:
            cmd["file_text"] = file_text
            
        if insert_line is not None:
            cmd["insert_line"] = insert_line
            
        if new_str is not None:
            cmd["new_str"] = new_str
            
        if old_str is not None:
            cmd["old_str"] = old_str

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def report_progress(
        self,
        commitMessage: Annotated[
            str,  
            "A short single line of text to use as the commit message"
        ],
        prDescription: Annotated[
            str,  
            "A description of work completed and remaining, using markdown checklists"
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        Report progress on the task. Call when you complete a meaningful unit of work. Commits and pushes changes that are pending in the repo, then updates the PR description.
        * Use this tool at least once, and as early as possible once you've established a plan. Outline the minimal-change plan as a checklist.
        * Use only when you have meaningful progress to report (you need to update the plan in the checklist, or you have completed a new item in the checklist)
        * Use markdown checklists to show progress (- [x] for completed items, - [ ] for pending items).
        * Keep the checklist structure as consistent as you can between updates, while still being accurate and useful.
        * Include 'Fixes #42.' as the last line of the body in the PR description.
        * Don't use headers in the PR description, just the checklist.
        * If there are changes in the repo this tool will run `git add .`, `git commit -m <msg>`, and `git push`.
        """
        cmd = {"commitMessage": commitMessage, "prDescription": prDescription}

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["report_progress", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def bash(
        self,
        command: Annotated[
            str,  
            "The bash command and arguments to run."
        ],
        sessionId: Annotated[
            str,  
            "Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time."
        ],
        async_run: Annotated[
            bool,  
            "If true, runs the command asynchronously. You can send input to the command using the `write_bash` tool and read its output using the `read_bash` tool."
        ],
        timeout: Annotated[
            int | None,
            "(Optional) Maximum time in seconds to wait for the command to complete when 'async' is false. Default is 120 seconds if not provided."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Runs a bash command in an interactive bash session.
        * When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.
        * You don't have access to the internet via this tool.
        * You can run Python, Node.js and Go code with the `python`, `node` and `go` commands.
        * You can install Linux, Python, JavaScript and Go packages with the `apt`, `pip`, `npm` and `go` commands.
        * Each sessionId identifies a persistent bash session. State is saved across command calls and discussions with the user.
        * `timeout` parameter must be greater than the default timeout of 120 seconds and less than 600 seconds. Give long-running commands enough time to complete.
        * If the command does not complete within "timeout" seconds, the tool will return a status indicating that it is still running asynchronously. You can then use `read_bash` or `stop_bash`.
        """
        if timeout is None:
            timeout = 120
        cmd = {"command": command, "sessionId": sessionId, "async": async_run, "timeout": timeout} # need to use 'async' here, 'async_run' is for not conflicting with Python's async keyword

        timeout_ms = timeout * 1000  # Convert to milliseconds
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd, timeout=timeout_ms):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def write_bash(
        self,
        sessionId: Annotated[
            str,  
            "Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time."
        ],
        input: Annotated[
            str,  
            "The input to send to the command or session."
        ],
        delay: Annotated[
            int | None,
            "(Optional) The amount of time in seconds to wait before reading the output that resulted from the input."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Sends input to the specified command or bash session.
        * This tool can be used to send input to a running bash command or an interactive console app.
        * Bash commands are run in an interactive bash session with a TTY device and bash command processor.
        * sessionId (required) must match the sessionId used to invoke the async bash command.
        * You can send text, {up}, {down}, {left}, {right}, {enter}, and {backspace} as input.
        * Some applications present a list of options to select from. The selection is often denoted using >, or different formatting.
        * When presented with a list of items, you must make a selection by sending arrow keys like {up} or {down} to move the selection to the item that you want to select and then {enter} to select it.
        * The response will contain any output read after "delay" seconds. Delay should be appropriate for the task and never less than 10 seconds.
        """
        cmd = {"sessionId": sessionId, "input": input}
        if delay is None:
            delay = 10
        cmd["delay"] = delay 
        timeout = (delay+5) * 1000  # Convert to milliseconds

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_write_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def read_bash(
        self,
        sessionId: Annotated[
            str,  
            "Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time."
        ],
        delay: Annotated[
            int | None,
            "(Optional) The amount of time in seconds to wait before reading the output that resulted from the input."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Reads output from a bash command.
        * Reads the output of a command running in an "async" bash session.
        * The sessionId must be the same one used to invoke the bash command.
        * You can call this tool multiple times to read output produced since the last call.
        * Each request has a cost, so provide a reasonable "delay" parameter value for the task, to minimize the need for repeated reads that return no output.
        * If a read request generates no output, consider using exponential backoff in choosing the delay between reads of the same command.
        * Though `write_bash` accepts ANSI control codes, this tool does not include them in the output.
        """
        cmd = {"sessionId": sessionId}
        if delay is None:
            delay = 10
        cmd["delay"] = delay 
        timeout = (delay+5) * 1000  # Convert to milliseconds

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_read_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def stop_bash(
        self,
        sessionId: Annotated[
            str,  
            "Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        Stops a running bash command.
        * Stops a running bash command by terminating the entire bash session and process.
        * This tool can be used to stop commands that have not exited on their own.
        * Any environment variables defined will have to be redefined after using this tool if the same session ID is used to run a new command.
        * The sessionId must match the sessionId used to invoke the bash command.
        """
        cmd = {"sessionId": sessionId}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_stop_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)
    
    
    # For applying penalties to bash command and enforcing forbidden commands
    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:
        
        # if sample.conversation and sample.conversation.messages[-1].recipient == "functions.str_replace_editor":
        #     try:
        #         args = json.loads(str(sample.conversation.messages[-1].content))
        #         args_cmd = args.get("command")
        #     except Exception:
        #         args_cmd = ""
        #     if args_cmd and args_cmd not in STR_EDITOR_ALLOWED_COMMANDS:
        #         # The sample attempted to execute a bash command.
        #         # Raise a ModelError to abort further processing.
        #         raise errors.ModelError(
        #             label="wrong_command_in_str_replace_editor",
        #             description=f"Attempted to call {args_cmd} in str_replace_editor tool command",
        #             sample=sample,
        #         )

        # Check if the sample has a conversation and if the last message is a function call
        if forbidden_commands := sample.gt_datapoint.metadata.get(ENFORCE_COMMANDS_FORBIDDEN):
            try:
                assert sample.conversation
                if sample.conversation.messages[-1].recipient == "functions.bash":
                    args = json.loads(str(sample.conversation.messages[-1].content))
                    args_cmd = args.get("command")
                else:
                    args_cmd = None
            except Exception:
                args_cmd = ""

            if args_cmd:
                for forbidden_command in forbidden_commands:
                    if args_cmd.startswith(forbidden_command):
                        # The sample attempted to execute a forbidden command.
                        # Raise a ModelError to abort further processing.
                        raise errors.ModelError(
                            label="used_enforced_forbidden_command",
                            description="Attempted to call a forbidden tool command",
                            sample=sample,
                        )

        sample.metrics.setdefault("tool_penalty", 0.0)  # For correct wandb average
        async for message in super()._process_sample_inner(sample):
            yield message

            # Only apply tool penalties starting from step 10 onwards
            try:
                if (policy_step := sample.metadata.get("sampling/policy_step_initial")) and (
                    "exec_cmd" in message.metadata
                ):
                    # This message is the first **response** tool message
                    multiplier = min(1.0, policy_step / TOOL_PENALTY_ANNEAL_STEPS)
                    # (Penalty must be negative!)
                    penalty = (
                        multiplier * self.tool_penalty_multiplier * calculate_tool_penalty(message)
                    )
                    sample.metrics["tool_penalty"] -= penalty
                    if (
                        self.max_tool_penalty is not None
                        and sample.metrics["tool_penalty"] < -self.max_tool_penalty
                    ):
                        sample.metrics["tool_penalty"] = -self.max_tool_penalty
            except Exception:
                logger.exception("Error applying tool penalty", exc_info=True)

    def parse_function_call(self, message: chat.Message) -> tuple[str, Callable, dict[str, Any]]:
        """
        Parse this message as a call to one of the functions in this tool.

        Raises `chat.ToolInputError` if the message does not parse
        successfully, if the message has an unrecognized recipient, etc.
        """

        # Figure out which function the model is calling.
        recipient = message.recipient
        if "." not in recipient:
            raise chat.ToolInputError(
                f"Invalid {recipient=}, expected '{self.get_tool_name()}.<function_name>'"
            )
        tool_name, function_name = recipient.split(".", 1)
        if tool_name != self.get_tool_name():
            raise chat.ToolInputError(
                f"Wrong {tool_name=} (with {function_name=}), expected '{self.get_tool_name()}'"
            )
        fn = getattr(self.__class__, function_name, None)
        if fn is None:
            raise chat.ToolInputError(f"Unrecognized function name '{function_name}'")
        if not hasattr(fn, "__fn_calling_tool_fn_type__"):
            raise chat.ToolInputError(f"Model cannot call {function_name=}")
        json_string = chat.render.common.render_content(message, msg_idx=0)
        try:
            json.loads(json_string, object_pairs_hook=_reject_format)
        except ValueError as e:
            raise chat.ToolInputError(f"Could not parse args as JSON: {e}") from e
        try:
            kwargs = orjson.loads(json_string)
        except orjson.JSONDecodeError as e:
            raise chat.ToolInputError(f"Could not parse args as JSON: {e}") from e
        function_schema = self.get_function_calling_function_schema(function_name)
        assert function_schema is not None
        if not function_schema.validate_json(kwargs):
            raise chat.ToolInputError(
                f"Invalid {function_name=} call: {kwargs=}. Expected: {function_schema.to_typescript()}"
            )
        return function_name, fn, kwargs

# fix grep in command: 
# Hook that spots format checks inside each JSON object
def _reject_format(pairs):
    seen = {}
    for k, v in pairs:
        if k in seen:                       # ← duplicate!
            raise ValueError(f"duplicate key {k!r}")
        
        if k == 'command' and isinstance(v, str):
            if 'bash' in v:
                raise ValueError(f"forbidden command 'bash' in {k!r}; this function tool call already enforces 'bash' in execution.")
        seen[k] = v
    return seen


# Tool penalty configuration
TOOL_PENALTY_ANNEAL_STEPS = 5
TOOL_PENALTY_BASE = 0.02
def calculate_tool_penalty(message: chat.Message) -> float:
    """
    Computes an aux reward penalty for the **response** to a container.exec message.
    `exec_cmd`, `exec_status_code`, `exec_duration_seconds` are populated by CaasContainerTool.
    """
    
    unwanted_cmds_in_bash = ['sed', "oai", 'cat', 'bash', "apply-patch", "apply_patch", 'nano', 'head', 'tail']
    # Check if message is calling any of the bash tool variants
    if message.recipient == "functions.bash":
        try:
            args = json.loads(str(message.content), object_pairs_hook=_reject_format)
            # Different bash variants use different parameter names for the command
            args_cmd = args.get("command") or args.get("c") or args.get("cmd") or args.get("run") or args.get("cmd_line") or args.get("script") or args.get("instruction")
            if args_cmd and 'bash' in args_cmd:
                return TOOL_PENALTY_BASE * 4
            if args_cmd and any(c in args_cmd for c in unwanted_cmds_in_bash):
                return TOOL_PENALTY_BASE * 3
        except Exception:
            return TOOL_PENALTY_BASE * 3
    
    # Penalty for model trying to call a function wrongly
    if message.recipient == "functions.str_replace_editor":
        try:
            args = json.loads(str(message.content), object_pairs_hook=_reject_format)
            args_cmd = args.get("command")
            if args_cmd and args_cmd not in STR_EDITOR_ALLOWED_COMMANDS:
                return TOOL_PENALTY_BASE * 3
        except Exception:
            return TOOL_PENALTY_BASE * 3
    
    error_messages = PADAWAN_TOOL_ERROR_MSGS
    if message.author.role == 'tool':
        response = render_content(message)
        if any(c in response for c in error_messages):
            return TOOL_PENALTY_BASE * 2
    
    tool_penalty: float = 0.0
    if (code := message.metadata.get("exec_status_code")) and code >= 124:
        # Timeout, not found, etc. usually indicate command misuse
        tool_penalty = TOOL_PENALTY_BASE
    if (duration := message.metadata.get("exec_duration_seconds")) and duration > 30:
        # Long commands come with a cost
        tool_penalty = TOOL_PENALTY_BASE

    return tool_penalty


@chz.chz(typecheck=True)
class PadawanToolConfig(OriginalCaasContainerToolConfig):
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown", "enabled"] = "no_access"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 600_000

    def get_tool_name(self) -> str:
        return DeepSWECaasPadawanTool.get_tool_name()

    def unique_descriptor_for_variants(self) -> str:
        return DeepSWECaasPadawanTool.get_tool_name()

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = DeepSWECaasPadawanTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        return tool.instruction()

    def get_names_of_functions_the_model_can_call(self) -> Collection[str]:
        return DeepSWECaasPadawanTool.get_names_of_functions_the_model_can_call()

    @property
    def _caas_resource_name(self) -> str:
        return _caas_container_resource_name(
            caas_network=self.caas_network,
            enable_network_after_setup=self.internet_policy == "enabled",
        )

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = DeepSWECaasPadawanTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool
    
