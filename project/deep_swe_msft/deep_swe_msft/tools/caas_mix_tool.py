import functools
import random
import json
import shlex
import difflib
from typing import Any, AsyncIterator, Collection, Literal, final

import structlog
import orjson
import chat
import chz
from caas_tool.caas_container import CaasContainer
from chat.render.common import render_content
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from berry_harmony.tools.berry_tool_interface import <PERSON><PERSON><PERSON>
from berry_caas_container_tool.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from berry_caas_container_tool.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
    _caas_container_resource_name
)
from berry_caas_container_tool.caas_container_tool import (
    CaasToolPluginConfig,
    create_caas_tool_plugins,
)

from abc import abstractmethod
from typing import Annotated, AsyncIterator, ClassVar, Iterator, Sequence, Literal, Callable
from function_calling_tool import Function<PERSON><PERSON>ingTool, function_the_model_can_call
from functions import Function, FunctionNamespace, from_python

from deep_swe_msft.padawan_data.datapoint_converters import ENFORCE_COMMANDS_FORBIDDEN
from deep_swe_msft.tools.vscode_copilot_tool import exec as caas_exec
from deep_swe_msft.tools.utils import PADAWAN_TOOL_ERROR_MSGS

logger = structlog.stdlib.get_logger(component=__name__)

TOOL_MAXTOKENS = 8192

EDITOR_VARIANTS = ['str_replace_editor', 'text_file_editor', 'code_file_modifier', 'content_manager', 'file_handler', 'script_editor']
EDITOR_SET_VARIANTS = [
    ['file_viewer', 'file_creator', 'text_replacer', 'content_inserter', 'edit_undoer'],
    ['show_file', 'make_file', 'change_text', 'add_content', 'revert_edit'],
    ['inspect_file', 'build_file', 'modify_text', 'inject_text', 'undo_change'],
    ['read_document', 'write_document', 'replace_content', 'insert_lines', 'rollback_edit']
]
BASH_VARIANTS = [
    ['bash', 'write_bash', 'read_bash', 'stop_bash'],
    ['shell_exec', 'shell_write', 'shell_read', 'shell_stop'],
    ['terminal_cmd', 'term_input', 'term_output', 'term_kill'],
    ['cmd_runner', 'cmd_send', 'cmd_poll', 'cmd_halt'],
    ['execute_command', 'send_input', 'check_output', 'terminate_process'],
    ['run_shell', 'pipe_data', 'fetch_result', 'end_session'],
    ['system_call', 'sys_write', 'sys_read', 'sys_terminate']
]
REPORT_VARIANTS = ['report_progress']
VIEW_VARIANTS = []

def _process_tool_message(message: str | chat.Message) -> str:
    """
    Truncate the message to a maximum of TOOL_MAXTOKENS tokens.
    """
    if isinstance(message, str):
        if len(message) > TOOL_MAXTOKENS:
            # Truncate in the middle, keeping beginning and end
            truncate_marker = " <truncated...> "
            available_space = TOOL_MAXTOKENS - len(truncate_marker)
            start_length = available_space // 2
            end_length = available_space - start_length
            message = message[:start_length] + truncate_marker + message[-end_length:]
    else:
        if len(message.content.parts[0]) > TOOL_MAXTOKENS:
            # Truncate in the middle, keeping beginning and end
            truncate_marker = " <truncated...> "
            available_space = TOOL_MAXTOKENS - len(truncate_marker)
            start_length = available_space // 2
            end_length = available_space - start_length
            message.content.parts[0] = (message.content.parts[0][:start_length] + 
                                      truncate_marker + 
                                      message.content.parts[0][-end_length:])
    return message

def _get_random_tool_set() -> set[str]:
    """
    Randomly selects a set of tools from the available variants.
    Ensures at least one tool from each essential category is included.
    """
    result = set()

    # Always ensure we have one tool from each essential category
    # Edit tool (required) - choose between individual variant or complete set
    edit_choice = random.choice(['individual', 'set'])
    if edit_choice == 'individual':
        selected_edit_tool = random.choice(EDITOR_VARIANTS)
        result.add(selected_edit_tool)
    else:
        # Select a complete editor set (5 functions)
        selected_editor_set = random.choice(EDITOR_SET_VARIANTS)
        for tool in selected_editor_set:
            result.add(tool)
    
    # Bash tool (required) - randomly select one variant set
    selected_bash_set = random.choice(BASH_VARIANTS)
    for tool in selected_bash_set:
        result.add(tool)
    
    # Report tool (required) - always add report_progress
    result.add('report_progress')

    if random.random() < 0.5:
        shuffle_result = list(result)
        random.shuffle(shuffle_result)
        result = set(shuffle_result)

    return list(result)


class MixTool(OriginalBerryCaasContainerTool):
    """
    Applies tool penalties and (forbidden) command enforcement!

    NOTE: Required command enforcement is done in IFEnforcementGrader;
    however failing forbidden commands immediately is more efficient and is a sort of weak credit assignment.
    """

    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 10_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = 'default',
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set

    # Remove feed_chars from the tool instructions
    @classmethod
    @functools.cache
    def get_names_of_functions_the_model_can_call(cls) -> Collection[str]:
        return _get_random_tool_set()
    
    @classmethod
    def get_tool_name(cls) -> str:
        return "functions"
    
    @classmethod
    def get_description(cls) -> str | None:
        return 'Microsoft Padawan tool for code generation and editing'
    
    @function_the_model_can_call
    async def str_replace_editor(
        self,
        command: Annotated[
            Literal["view", "create", "str_replace", "insert", "undo_edit"],  
            "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`."
        ],
        path: Annotated[
            str,  
            "Absolute path to file or directory."
        ],
        file_text: Annotated[
            str | None,  
            "Required parameter of `create` command, with the content of the file to be created."
        ] = None,
        insert_line: Annotated[
            int | None,  
            "Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`."
        ] = None,
        new_str: Annotated[
            str | None,  
            "Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert."
        ] = None,
        old_str: Annotated[
            str | None,  
            "Required parameter of `str_replace` command containing the string in `path` to replace. Leading and ending whitespaces from file content should be preserved!"
        ] = None,
        view_range: Annotated[
            list[int] | None,  
            "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Custom editing tool for viewing, creating and editing files
        * State is persistent across command calls and discussions with the user
        * If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep
        * The `create` command cannot be used if the specified `path` already exists as a file
        * If a `command` generates a long output, it will be clipped in the middle and marked with `<file too long...`
        * The `undo_edit` command will revert the last edit made to the file at `path`
        Notes for using the `str_replace` command:
        * The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file.
        * If the `old_str` parameter is not unique in the file, the replacement will not be performed. Make sure to include enough context in `old_str` to make it unique
        * The `new_str` parameter should contain the edited lines that should replace the `old_str`
        """
        cmd = {"command": command, "path": path}
        
        if view_range is not None:
            cmd["view_range"] = view_range
            
        if file_text is not None:
            cmd["file_text"] = file_text
            
        if insert_line is not None:
            cmd["insert_line"] = insert_line
            
        if new_str is not None:
            cmd["new_str"] = new_str
            
        if old_str is not None:
            cmd["old_str"] = old_str

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def text_file_editor(
        self,
        op: Annotated[
            Literal["read", "write", "replace", "add", "revert"],  
            "Operation type. Options: `read`, `write`, `replace`, `add`, `revert`."
        ],
        p: Annotated[
            str,  
            "File or directory path (absolute)."
        ],
        txt: Annotated[
            str | None,  
            "Text content for `write` operation."
        ] = None,
        ln: Annotated[
            int | None,  
            "Line number for `add` operation. Text inserted after this line."
        ] = None,
        new: Annotated[
            str | None,  
            "New text for `replace` or content for `add` operation."
        ] = None,
        old: Annotated[
            str | None,  
            "Original text to replace in `replace` operation."
        ] = None,
        rng: Annotated[
            list[int] | None,  
            "Line range for `read` operation, e.g. [1, 10]."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Text file editing utility with compact interface
        * `read` shows file content or directory listing
        * `write` creates new file with provided content
        * `replace` substitutes old text with new text
        * `add` inserts content after specified line
        * `revert` undoes last modification
        """
        cmd_map = {"read": "view", "write": "create", "replace": "str_replace", "add": "insert", "revert": "undo_edit"}
        cmd = {"command": cmd_map[op], "path": p}
        
        if rng is not None:
            cmd["view_range"] = rng
        if txt is not None:
            cmd["file_text"] = txt
        if ln is not None:
            cmd["insert_line"] = ln
        if new is not None:
            cmd["new_str"] = new
        if old is not None:
            cmd["old_str"] = old

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def code_file_modifier(
        self,
        cmd: Annotated[
            Literal["show", "make", "edit", "append", "undo"],  
            "Command to execute: `show`, `make`, `edit`, `append`, `undo`."
        ],
        f: Annotated[
            str,  
            "Target file path."
        ],
        data: Annotated[
            str | None,  
            "File content for `make` command."
        ] = None,
        pos: Annotated[
            int | None,  
            "Position for `append` command."
        ] = None,
        upd: Annotated[
            str | None,  
            "Updated content for `edit` or `append` command."
        ] = None,
        orig: Annotated[
            str | None,  
            "Original content to modify in `edit` command."
        ] = None,
        span: Annotated[
            list[int] | None,  
            "Line span for `show` command."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Code modification tool with streamlined parameters
        * `show` displays file or directory contents
        * `make` creates new file with specified data
        * `edit` replaces original content with updated version
        * `append` adds content at specified position
        * `undo` reverses last change
        """
        cmd_map = {"show": "view", "make": "create", "edit": "str_replace", "append": "insert", "undo": "undo_edit"}
        command = {"command": cmd_map[cmd], "path": f}
        
        if span is not None:
            command["view_range"] = span
        if data is not None:
            command["file_text"] = data
        if pos is not None:
            command["insert_line"] = pos
        if upd is not None:
            command["new_str"] = upd
        if orig is not None:
            command["old_str"] = orig

        cmd_arguments = json.dumps(command)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def content_manager(
        self,
        op: Annotated[
            Literal["view", "write", "update", "add", "undo"],  
            "Operation: `view`, `write`, `update`, `add`, `undo`."
        ],
        file: Annotated[
            str,  
            "File path to manage."
        ],
        text: Annotated[
            str | None,  
            "Text content for `write` operation."
        ] = None,
        line: Annotated[
            int | None,  
            "Line number for `add` operation."
        ] = None,
        new: Annotated[
            str | None,  
            "New text for `update` or content for `add`."
        ] = None,
        old: Annotated[
            str | None,  
            "Old text to find in `update` operation."
        ] = None,
        span: Annotated[
            list[int] | None,  
            "Line range for `view` operation."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Content management system for file operations
        * `view` displays file content within specified range
        * `write` creates new file with provided text
        * `update` replaces old text with new text
        * `add` inserts content at specified line
        * `undo` reverts the last operation
        """
        op_map = {"view": "view", "write": "create", "update": "str_replace", "add": "insert", "undo": "undo_edit"}
        cmd = {"command": op_map[op], "path": file}
        
        if span is not None:
            cmd["view_range"] = span
        if text is not None:
            cmd["file_text"] = text
        if line is not None:
            cmd["insert_line"] = line
        if new is not None:
            cmd["new_str"] = new
        if old is not None:
            cmd["old_str"] = old

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def file_handler(
        self,
        mode: Annotated[
            Literal["peek", "build", "swap", "insert", "reset"],  
            "Mode of operation: `peek`, `build`, `swap`, `insert`, `reset`."
        ],
        loc: Annotated[
            str,  
            "File location."
        ],
        text: Annotated[
            str | None,  
            "Text for `build` mode."
        ] = None,
        idx: Annotated[
            int | None,  
            "Index for `insert` mode."
        ] = None,
        fresh: Annotated[
            str | None,  
            "Fresh content for `swap` or `insert` mode."
        ] = None,
        stale: Annotated[
            str | None,  
            "Stale content to replace in `swap` mode."
        ] = None,
        window: Annotated[
            list[int] | None,  
            "View window for `peek` mode."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Compact file handling tool
        * `peek` views file content within optional window
        * `build` constructs new file with text
        * `swap` exchanges stale content with fresh content
        * `insert` adds fresh content at index
        * `reset` undoes last modification
        """
        mode_map = {"peek": "view", "build": "create", "swap": "str_replace", "insert": "insert", "reset": "undo_edit"}
        cmd = {"command": mode_map[mode], "path": loc}
        
        if window is not None:
            cmd["view_range"] = window
        if text is not None:
            cmd["file_text"] = text
        if idx is not None:
            cmd["insert_line"] = idx
        if fresh is not None:
            cmd["new_str"] = fresh
        if stale is not None:
            cmd["old_str"] = stale

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def script_editor(
        self,
        cmd: Annotated[
            Literal["show", "make", "change", "insert", "undo"],  
            "Command: `show`, `make`, `change`, `insert`, `undo`."
        ],
        path: Annotated[
            str,  
            "Script file path."
        ],
        content: Annotated[
            str | None,  
            "File content for `make` command."
        ] = None,
        pos: Annotated[
            int | None,  
            "Line position for `insert` command."
        ] = None,
        replace: Annotated[
            str | None,  
            "Replacement text for `change` or insert content for `insert`."
        ] = None,
        find: Annotated[
            str | None,  
            "Text to find for `change` command."
        ] = None,
        lines: Annotated[
            list[int] | None,  
            "Line range for `show` command."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Script editing interface for code manipulation
        * `show` reveals script content in specified line range
        * `make` creates new script with provided content
        * `change` replaces found text with replacement
        * `insert` adds content at position
        * `undo` reverses last edit
        """
        cmd_map = {"show": "view", "make": "create", "change": "str_replace", "insert": "insert", "undo": "undo_edit"}
        command = {"command": cmd_map[cmd], "path": path}
        
        if lines is not None:
            command["view_range"] = lines
        if content is not None:
            command["file_text"] = content
        if pos is not None:
            command["insert_line"] = pos
        if replace is not None:
            command["new_str"] = replace
        if find is not None:
            command["old_str"] = find
        cmd_arguments = json.dumps(command)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # Editor Set 1: file_* functions
    @function_the_model_can_call
    async def file_viewer(
        self,
        path: Annotated[str, "File or directory path"],
        view_range: Annotated[list[int] | None, "Line range [start, end]"] = None,
    ) -> AsyncIterator[chat.Message]:
        """View file contents or directory listing"""
        cmd = {"command": "view", "path": path}
        if view_range is not None:
            cmd["view_range"] = view_range
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def file_creator(
        self,
        path: Annotated[str, "File path to create"],
        file_text: Annotated[str, "Content for new file"],
    ) -> AsyncIterator[chat.Message]:
        """Create new file with content"""
        cmd = {"command": "create", "path": path, "file_text": file_text}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def text_replacer(
        self,
        path: Annotated[str, "File path"],
        old_str: Annotated[str, "Text to replace"],
        new_str: Annotated[str, "Replacement text"],
    ) -> AsyncIterator[chat.Message]:
        """Replace text in file"""
        cmd = {"command": "str_replace", "path": path, "old_str": old_str, "new_str": new_str}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def content_inserter(
        self,
        path: Annotated[str, "File path"],
        insert_line: Annotated[int, "Line number to insert after"],
        new_str: Annotated[str, "Text to insert"],
    ) -> AsyncIterator[chat.Message]:
        """Insert text after specified line"""
        cmd = {"command": "insert", "path": path, "insert_line": insert_line, "new_str": new_str}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def edit_undoer(
        self,
        path: Annotated[str, "File path"],
    ) -> AsyncIterator[chat.Message]:
        """Undo last edit to file"""
        cmd = {"command": "undo_edit", "path": path}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # Editor Set 2: show_/make_/change_/add_/revert_ functions
    @function_the_model_can_call
    async def show_file(
        self,
        target: Annotated[str, "Target file or directory"],
        lines: Annotated[list[int] | None, "Line range to show"] = None,
    ) -> AsyncIterator[chat.Message]:
        """Show file or directory contents"""
        cmd = {"command": "view", "path": target}
        if lines is not None:
            cmd["view_range"] = lines
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def make_file(
        self,
        target: Annotated[str, "File to create"],
        content: Annotated[str, "File content"],
    ) -> AsyncIterator[chat.Message]:
        """Make new file with content"""
        cmd = {"command": "create", "path": target, "file_text": content}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def change_text(
        self,
        target: Annotated[str, "File to modify"],
        original: Annotated[str, "Original text"],
        updated: Annotated[str, "Updated text"],
    ) -> AsyncIterator[chat.Message]:
        """Change text in file"""
        cmd = {"command": "str_replace", "path": target, "old_str": original, "new_str": updated}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def add_content(
        self,
        target: Annotated[str, "File to modify"],
        after_line: Annotated[int, "Line to insert after"],
        content: Annotated[str, "Content to add"],
    ) -> AsyncIterator[chat.Message]:
        """Add content after line"""
        cmd = {"command": "insert", "path": target, "insert_line": after_line, "new_str": content}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def revert_edit(
        self,
        target: Annotated[str, "File to revert"],
    ) -> AsyncIterator[chat.Message]:
        """Revert last edit"""
        cmd = {"command": "undo_edit", "path": target}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # Editor Set 3: inspect_/build_/modify_/inject_/undo_ functions
    @function_the_model_can_call
    async def inspect_file(
        self,
        location: Annotated[str, "File or directory location"],
        scope: Annotated[list[int] | None, "Line scope to inspect"] = None,
    ) -> AsyncIterator[chat.Message]:
        """Inspect file or directory"""
        cmd = {"command": "view", "path": location}
        if scope is not None:
            cmd["view_range"] = scope
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def build_file(
        self,
        location: Annotated[str, "File location"],
        data: Annotated[str, "File data"],
    ) -> AsyncIterator[chat.Message]:
        """Build new file"""
        cmd = {"command": "create", "path": location, "file_text": data}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def modify_text(
        self,
        location: Annotated[str, "File location"],
        source: Annotated[str, "Source text"],
        target: Annotated[str, "Target text"],
    ) -> AsyncIterator[chat.Message]:
        """Modify text in file"""
        cmd = {"command": "str_replace", "path": location, "old_str": source, "new_str": target}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def inject_text(
        self,
        location: Annotated[str, "File location"],
        position: Annotated[int, "Line position"],
        text: Annotated[str, "Text to inject"],
    ) -> AsyncIterator[chat.Message]:
        """Inject text at position"""
        cmd = {"command": "insert", "path": location, "insert_line": position, "new_str": text}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def undo_change(
        self,
        location: Annotated[str, "File location"],
    ) -> AsyncIterator[chat.Message]:
        """Undo last change"""
        cmd = {"command": "undo_edit", "path": location}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # Editor Set 4: read_/write_/replace_/insert_/rollback_ functions
    @function_the_model_can_call
    async def read_document(
        self,
        doc_path: Annotated[str, "Document path"],
        range_spec: Annotated[list[int] | None, "Range specification"] = None,
    ) -> AsyncIterator[chat.Message]:
        """Read document contents"""
        cmd = {"command": "view", "path": doc_path}
        if range_spec is not None:
            cmd["view_range"] = range_spec
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def write_document(
        self,
        doc_path: Annotated[str, "Document path"],
        doc_content: Annotated[str, "Document content"],
    ) -> AsyncIterator[chat.Message]:
        """Write new document"""
        cmd = {"command": "create", "path": doc_path, "file_text": doc_content}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def replace_content(
        self,
        doc_path: Annotated[str, "Document path"],
        old_content: Annotated[str, "Old content"],
        new_content: Annotated[str, "New content"],
    ) -> AsyncIterator[chat.Message]:
        """Replace document content"""
        cmd = {"command": "str_replace", "path": doc_path, "old_str": old_content, "new_str": new_content}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def insert_lines(
        self,
        doc_path: Annotated[str, "Document path"],
        line_num: Annotated[int, "Line number"],
        lines: Annotated[str, "Lines to insert"],
    ) -> AsyncIterator[chat.Message]:
        """Insert lines in document"""
        cmd = {"command": "insert", "path": doc_path, "insert_line": line_num, "new_str": lines}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def rollback_edit(
        self,
        doc_path: Annotated[str, "Document path"],
    ) -> AsyncIterator[chat.Message]:
        """Rollback last edit"""
        cmd = {"command": "undo_edit", "path": doc_path}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def report_progress(
        self,
        commitMessage: Annotated[
            str,  
            "A short single line of text to use as the commit message"
        ],
        prDescription: Annotated[
            str,  
            "A description of work completed and remaining, using markdown checklists"
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        Report progress on the task. Call when you complete a meaningful unit of work. Commits and pushes changes that are pending in the repo, then updates the PR description.
        * Use this tool at least once, and as early as possible once you've established a plan. Outline the minimal-change plan as a checklist.
        * Use only when you have meaningful progress to report (you need to update the plan in the checklist, or you have completed a new item in the checklist)
        * Use markdown checklists to show progress (- [x] for completed items, - [ ] for pending items).
        * Keep the checklist structure as consistent as you can between updates, while still being accurate and useful.
        * Include 'Fixes #42.' as the last line of the body in the PR description.
        * Don't use headers in the PR description, just the checklist.
        * If there are changes in the repo this tool will run `git add .`, `git commit -m <msg>`, and `git push`.
        """
        cmd = {"commitMessage": commitMessage, "prDescription": prDescription}

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["report_progress", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def bash(
        self,
        command: Annotated[
            str,  
            "The bash command and arguments to run."
        ],
        sessionId: Annotated[
            str,  
            "Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time."
        ],
        async_run: Annotated[
            bool,  
            "If true, runs the command asynchronously. You can send input to the command using the `write_bash` tool and read its output using the `read_bash` tool."
        ],
        timeout: Annotated[
            int | None,
            "(Optional) Maximum time in seconds to wait for the command to complete when 'async' is false. Default is 120 seconds if not provided."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Runs a bash command in an interactive bash session.
        * When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.
        * You don't have access to the internet via this tool.
        * You can run Python, Node.js and Go code with the `python`, `node` and `go` commands.
        * You can install Linux, Python, JavaScript and Go packages with the `apt`, `pip`, `npm` and `go` commands.
        * Each sessionId identifies a persistent bash session. State is saved across command calls and discussions with the user.
        * `timeout` parameter must be greater than the default timeout of 120 seconds and less than 600 seconds. Give long-running commands enough time to complete.
        * If the command does not complete within "timeout" seconds, the tool will return a status indicating that it is still running asynchronously. You can then use `read_bash` or `stop_bash`.
        """
        if timeout is None:
            timeout = 120
        cmd = {"command": command, "sessionId": sessionId, "async": async_run, "timeout": timeout}

        timeout_ms = timeout * 1000  # Convert to milliseconds
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd, timeout=timeout_ms):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def write_bash(
        self,
        sessionId: Annotated[
            str,  
            "Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time."
        ],
        input: Annotated[
            str,  
            "The input to send to the command or session."
        ],
        delay: Annotated[
            int | None,
            "(Optional) The amount of time in seconds to wait before reading the output that resulted from the input."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Sends input to the specified command or bash session.
        * This tool can be used to send input to a running bash command or an interactive console app.
        * Bash commands are run in an interactive bash session with a TTY device and bash command processor.
        * sessionId (required) must match the sessionId used to invoke the async bash command.
        * You can send text, {up}, {down}, {left}, {right}, {enter}, and {backspace} as input.
        * Some applications present a list of options to select from. The selection is often denoted using >, or different formatting.
        * When presented with a list of items, you must make a selection by sending arrow keys like {up} or {down} to move the selection to the item that you want to select and then {enter} to select it.
        * The response will contain any output read after "delay" seconds. Delay should be appropriate for the task and never less than 10 seconds.
        """
        cmd = {"sessionId": sessionId, "input": input}
        if delay is None:
            delay = 10
        cmd["delay"] = delay 
        timeout = (delay+5) * 1000  # Convert to milliseconds

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_write_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def read_bash(
        self,
        sessionId: Annotated[
            str,  
            "Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time."
        ],
        delay: Annotated[
            int | None,
            "(Optional) The amount of time in seconds to wait before reading the output that resulted from the input."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Reads output from a bash command.
        * Reads the output of a command running in an "async" bash session.
        * The sessionId must be the same one used to invoke the bash command.
        * You can call this tool multiple times to read output produced since the last call.
        * Each request has a cost, so provide a reasonable "delay" parameter value for the task, to minimize the need for repeated reads that return no output.
        * If a read request generates no output, consider using exponential backoff in choosing the delay between reads of the same command.
        * Though `write_bash` accepts ANSI control codes, this tool does not include them in the output.
        """
        cmd = {"sessionId": sessionId}
        if delay is None:
            delay = 10
        cmd["delay"] = delay 
        timeout = (delay+5) * 1000  # Convert to milliseconds

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_read_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def stop_bash(
        self,
        sessionId: Annotated[
            str,  
            "Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        Stops a running bash command.
        * Stops a running bash command by terminating the entire bash session and process.
        * This tool can be used to stop commands that have not exited on their own.
        * Any environment variables defined will have to be redefined after using this tool if the same session ID is used to run a new command.
        * The sessionId must match the sessionId used to invoke the bash command.
        """
        cmd = {"sessionId": sessionId}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_stop_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)
    
    # Shell Exec Set (Variant 2)
    @function_the_model_can_call
    async def shell_exec(
        self,
        cmd: Annotated[
            str,  
            "Shell command to execute."
        ],
        sid: Annotated[
            str,  
            "Session identifier for the shell instance."
        ],
        bg: Annotated[
            bool,  
            "Run in background mode for async execution."
        ],
        t: Annotated[
            int | None,
            "Timeout in seconds (default: 120)."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Execute shell commands in persistent session
        * Supports multi-language development environments
        * Session state persists between command executions
        * Background execution for long-running processes
        * Package installation via standard managers
        """
        if t is None:
            t = 120
        cmd_dict = {"command": cmd, "sessionId": sid, "async": bg, "timeout": t}
        timeout_ms = t * 1000
        cmd_arguments = json.dumps(cmd_dict)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout_ms):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def shell_write(
        self,
        sid: Annotated[
            str,  
            "Session identifier."
        ],
        data: Annotated[
            str,  
            "Data to send to shell."
        ],
        wait: Annotated[
            int | None,
            "Wait time before reading response."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Send data to active shell session
        * Pipe input to running commands
        * Interactive application control
        * Navigation key support
        """
        cmd = {"sessionId": sid, "input": data}
        if wait is None:
            wait = 10
        cmd["delay"] = wait
        timeout = (wait+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_write_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def shell_read(
        self,
        sid: Annotated[
            str,  
            "Session identifier."
        ],
        wait: Annotated[
            int | None,
            "Wait duration before output fetch."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Read output from shell session
        * Retrieve command execution results
        * Monitor async process output
        * Efficient output polling
        """
        cmd = {"sessionId": sid}
        if wait is None:
            wait = 10
        cmd["delay"] = wait
        timeout = (wait+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_read_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def shell_stop(
        self,
        sid: Annotated[
            str,  
            "Session identifier."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        Stop shell session
        * Terminate running processes
        * Clean up session resources
        * Reset environment state
        """
        cmd = {"sessionId": sid}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_stop_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # Terminal Cmd Set (Variant 3)
    @function_the_model_can_call
    async def terminal_cmd(
        self,
        command: Annotated[
            str,  
            "Terminal command to run."
        ],
        term_id: Annotated[
            str,  
            "Terminal session ID."
        ],
        async_mode: Annotated[
            bool,  
            "Execute asynchronously."
        ],
        max_time: Annotated[
            int | None,
            "Maximum execution time."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Run commands in terminal environment
        * Persistent terminal sessions
        * Development tools integration
        * Async command execution
        """
        if max_time is None:
            max_time = 120
        cmd = {"command": command, "sessionId": term_id, "async": async_mode, "timeout": max_time}
        timeout_ms = max_time * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout_ms):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def term_input(
        self,
        term_id: Annotated[
            str,  
            "Terminal session ID."
        ],
        text: Annotated[
            str,  
            "Input text to send."
        ],
        pause: Annotated[
            int | None,
            "Pause duration in seconds."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Send input to terminal
        * Interactive command input
        * Control sequence support
        * Menu navigation
        """
        cmd = {"sessionId": term_id, "input": text}
        if pause is None:
            pause = 10
        cmd["delay"] = pause
        timeout = (pause+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_write_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def term_output(
        self,
        term_id: Annotated[
            str,  
            "Terminal session ID."
        ],
        pause: Annotated[
            int | None,
            "Pause before reading output."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Read terminal output
        * Capture command results
        * Stream processing output
        * Real-time monitoring
        """
        cmd = {"sessionId": term_id}
        if pause is None:
            pause = 10
        cmd["delay"] = pause
        timeout = (pause+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_read_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def term_kill(
        self,
        term_id: Annotated[
            str,  
            "Terminal session ID."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        Kill terminal session
        * Force terminate processes
        * Close session cleanly
        * Release resources
        """
        cmd = {"sessionId": term_id}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_stop_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # Cmd Runner Set (Variant 4)
    @function_the_model_can_call
    async def cmd_runner(
        self,
        run: Annotated[
            str,  
            "Command to run."
        ],
        session: Annotated[
            str,  
            "Session name."
        ],
        background: Annotated[
            bool,  
            "Background execution flag."
        ],
        duration: Annotated[
            int | None,
            "Max duration in seconds."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Execute commands with session management
        * Command execution interface
        * Session-based state management
        * Background process support
        """
        if duration is None:
            duration = 120
        cmd = {"command": run, "sessionId": session, "async": background, "timeout": duration}
        timeout_ms = duration * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout_ms):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def cmd_send(
        self,
        session: Annotated[
            str,  
            "Session name."
        ],
        payload: Annotated[
            str,  
            "Data payload to send."
        ],
        interval: Annotated[
            int | None,
            "Wait interval."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Send data to command session
        * Data transmission to active processes
        * Interactive session control
        * Input streaming
        """
        cmd = {"sessionId": session, "input": payload}
        if interval is None:
            interval = 10
        cmd["delay"] = interval
        timeout = (interval+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_write_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def cmd_poll(
        self,
        session: Annotated[
            str,  
            "Session name."
        ],
        interval: Annotated[
            int | None,
            "Polling interval."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Poll command output
        * Retrieve execution output
        * Status monitoring
        * Result polling
        """
        cmd = {"sessionId": session}
        if interval is None:
            interval = 10
        cmd["delay"] = interval
        timeout = (interval+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_read_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def cmd_halt(
        self,
        session: Annotated[
            str,  
            "Session name."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        Halt command execution
        * Stop running commands
        * Terminate session
        * Clean shutdown
        """
        cmd = {"sessionId": session}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_stop_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # Execute Command Set (Variant 5)
    @function_the_model_can_call
    async def execute_command(
        self,
        cmd_line: Annotated[
            str,  
            "Command line to execute."
        ],
        proc_id: Annotated[
            str,  
            "Process identifier."
        ],
        detached: Annotated[
            bool,  
            "Run as detached process."
        ],
        wait_time: Annotated[
            int | None,
            "Maximum wait time."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Execute command line instructions
        * Process management with identifiers
        * Detached execution mode
        * Environment state preservation
        """
        if wait_time is None:
            wait_time = 120
        cmd = {"command": cmd_line, "sessionId": proc_id, "async": detached, "timeout": wait_time}
        timeout_ms = wait_time * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout_ms):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def send_input(
        self,
        proc_id: Annotated[
            str,  
            "Process identifier."
        ],
        message: Annotated[
            str,  
            "Input message."
        ],
        latency: Annotated[
            int | None,
            "Response latency."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Send input to process
        * Message transmission to processes
        * Interactive process communication
        * Control signal support
        """
        cmd = {"sessionId": proc_id, "input": message}
        if latency is None:
            latency = 10
        cmd["delay"] = latency
        timeout = (latency+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_write_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def check_output(
        self,
        proc_id: Annotated[
            str,  
            "Process identifier."
        ],
        latency: Annotated[
            int | None,
            "Check latency."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Check process output
        * Output verification
        * Status checking
        * Result inspection
        """
        cmd = {"sessionId": proc_id}
        if latency is None:
            latency = 10
        cmd["delay"] = latency
        timeout = (latency+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_read_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def terminate_process(
        self,
        proc_id: Annotated[
            str,  
            "Process identifier."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        Terminate process
        * Force process termination
        * Resource cleanup
        * Process lifecycle management
        """
        cmd = {"sessionId": proc_id}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_stop_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # Run Shell Set (Variant 6)
    @function_the_model_can_call
    async def run_shell(
        self,
        script: Annotated[
            str,  
            "Shell script to run."
        ],
        env_id: Annotated[
            str,  
            "Environment identifier."
        ],
        daemon: Annotated[
            bool,  
            "Run as daemon process."
        ],
        max_wait: Annotated[
            int | None,
            "Maximum wait period."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Run shell scripts in managed environment
        * Script execution with environment isolation
        * Daemon mode for long-running tasks
        * Persistent environment variables
        """
        if max_wait is None:
            max_wait = 120
        cmd = {"command": script, "sessionId": env_id, "async": daemon, "timeout": max_wait}
        timeout_ms = max_wait * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout_ms):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def pipe_data(
        self,
        env_id: Annotated[
            str,  
            "Environment identifier."
        ],
        stream: Annotated[
            str,  
            "Data stream to pipe."
        ],
        buffer_time: Annotated[
            int | None,
            "Buffer time before flush."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Pipe data to shell environment
        * Stream data piping
        * Buffer management
        * Data flow control
        """
        cmd = {"sessionId": env_id, "input": stream}
        if buffer_time is None:
            buffer_time = 10
        cmd["delay"] = buffer_time
        timeout = (buffer_time+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_write_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def fetch_result(
        self,
        env_id: Annotated[
            str,  
            "Environment identifier."
        ],
        buffer_time: Annotated[
            int | None,
            "Buffer time before fetch."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Fetch execution results
        * Result retrieval
        * Output collection
        * Data aggregation
        """
        cmd = {"sessionId": env_id}
        if buffer_time is None:
            buffer_time = 10
        cmd["delay"] = buffer_time
        timeout = (buffer_time+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_read_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def end_session(
        self,
        env_id: Annotated[
            str,  
            "Environment identifier."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        End shell session
        * Session termination
        * Environment cleanup
        * Resource deallocation
        """
        cmd = {"sessionId": env_id}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_stop_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    # System Call Set (Variant 7)
    @function_the_model_can_call
    async def system_call(
        self,
        instruction: Annotated[
            str,  
            "System instruction to execute."
        ],
        sys_id: Annotated[
            str,  
            "System session identifier."
        ],
        nonblocking: Annotated[
            bool,  
            "Non-blocking execution mode."
        ],
        time_limit: Annotated[
            int | None,
            "Execution time limit."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Make system calls through controlled interface
        * System-level command execution
        * Non-blocking operation support
        * Controlled system access
        """
        if time_limit is None:
            time_limit = 120
        cmd = {"command": instruction, "sessionId": sys_id, "async": nonblocking, "timeout": time_limit}
        timeout_ms = time_limit * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout_ms):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def sys_write(
        self,
        sys_id: Annotated[
            str,  
            "System session identifier."
        ],
        content: Annotated[
            str,  
            "Content to write."
        ],
        flush_delay: Annotated[
            int | None,
            "Flush delay in seconds."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Write to system interface
        * System input/output operations
        * Buffer flush control
        * System communication
        """
        cmd = {"sessionId": sys_id, "input": content}
        if flush_delay is None:
            flush_delay = 10
        cmd["delay"] = flush_delay
        timeout = (flush_delay+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_write_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def sys_read(
        self,
        sys_id: Annotated[
            str,  
            "System session identifier."
        ],
        flush_delay: Annotated[
            int | None,
            "Read delay in seconds."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Read from system interface
        * System output reading
        * Status information retrieval
        * System response monitoring
        """
        cmd = {"sessionId": sys_id}
        if flush_delay is None:
            flush_delay = 10
        cmd["delay"] = flush_delay
        timeout = (flush_delay+5) * 1000
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_read_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def sys_terminate(
        self,
        sys_id: Annotated[
            str,  
            "System session identifier."
        ]
    ) -> AsyncIterator[chat.Message]:
        """
        Terminate system session
        * System session cleanup
        * Resource release
        * Safe system shutdown
        """
        cmd = {"sessionId": sys_id}
        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_stop_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:

        # Check if the sample has a conversation and if the last message is a function call
        if forbidden_commands := sample.gt_datapoint.metadata.get(ENFORCE_COMMANDS_FORBIDDEN):
            try:
                assert sample.conversation
                # Check if the last message is calling any bash execution tool (first tool in each variant set)
                bash_execution_tools = []
                for variant_set in BASH_VARIANTS:
                    bash_execution_tools.append(f"functions.{variant_set[0]}")  # Only the first tool (execution tool)
                
                if sample.conversation.messages[-1].recipient in bash_execution_tools:
                    args = json.loads(str(sample.conversation.messages[-1].content))
                    # Different bash variants use different parameter names for the command
                    args_cmd = (args.get("command") or args.get("c") or args.get("cmd") or 
                               args.get("run") or args.get("cmd_line") or args.get("script") or 
                               args.get("instruction"))
                else:
                    args_cmd = None
            except Exception:
                args_cmd = ""

            if args_cmd:
                for forbidden_command in forbidden_commands:
                    if args_cmd.startswith(forbidden_command):
                        # The sample attempted to execute a forbidden command.
                        # Raise a ModelError to abort further processing.
                        raise errors.ModelError(
                            label="used_enforced_forbidden_command",
                            description="Attempted to call a forbidden tool command",
                            sample=sample,
                        )

        sample.metrics.setdefault("tool_penalty", 0.0)  # For correct wandb average
        async for message in super()._process_sample_inner(sample):
            yield message

            # Only apply tool penalties starting from step 10 onwards
            try:
                if (policy_step := sample.metadata.get("sampling/policy_step_initial")) and (
                    "exec_cmd" in message.metadata
                ):
                    # This message is the first **response** tool message
                    multiplier = min(1.0, policy_step / TOOL_PENALTY_ANNEAL_STEPS)
                    # (Penalty must be negative!)
                    penalty = (
                        multiplier * self.tool_penalty_multiplier * calculate_tool_penalty(message)
                    )
                    sample.metrics["tool_penalty"] -= penalty
                    if (
                        self.max_tool_penalty is not None
                        and sample.metrics["tool_penalty"] < -self.max_tool_penalty
                    ):
                        sample.metrics["tool_penalty"] = -self.max_tool_penalty
            except Exception:
                logger.exception("Error applying tool penalty", exc_info=True)

    def parse_function_call(self, message: chat.Message) -> tuple[str, Callable, dict[str, Any]]:
        """
        Parse this message as a call to one of the functions in this tool.

        Raises `chat.ToolInputError` if the message does not parse
        successfully, if the message has an unrecognized recipient, etc.
        """

        # Figure out which function the model is calling.
        recipient = message.recipient
        if "." not in recipient:
            raise chat.ToolInputError(
                f"Invalid {recipient=}, expected '{self.get_tool_name()}.<function_name>'"
            )
        tool_name, function_name = recipient.split(".", 1)
        if tool_name != self.get_tool_name():
            raise chat.ToolInputError(
                f"Wrong {tool_name=} (with {function_name=}), expected '{self.get_tool_name()}'"
            )
        fn = getattr(self.__class__, function_name, None)
        if fn is None:
            raise chat.ToolInputError(f"Unrecognized function name '{function_name}'")
        if not hasattr(fn, "__fn_calling_tool_fn_type__"):
            raise chat.ToolInputError(f"Model cannot call {function_name=}")
        json_string = chat.render.common.render_content(message, msg_idx=0)
        try:
            json.loads(json_string, object_pairs_hook=_reject_format)
        except ValueError as e:
            raise chat.ToolInputError(f"Could not parse args as JSON: {e}") from e
        try:
            kwargs = orjson.loads(json_string)
        except orjson.JSONDecodeError as e:
            raise chat.ToolInputError(f"Could not parse args as JSON: {e}") from e
        function_schema = self.get_function_calling_function_schema(function_name)
        assert function_schema is not None
        if not function_schema.validate_json(kwargs):
            raise chat.ToolInputError(
                f"Invalid {function_name=} call: {kwargs=}. Expected: {function_schema.to_typescript()}"
            )
        return function_name, fn, kwargs

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        self._assert_valid_tool_name()
        return self.get_function_calling_namespace(datapoint).to_typescript()
    
    def get_function_calling_namespace(self, datapoint: HarmonyDatapoint | None = None) -> FunctionNamespace:
        name = self.get_tool_name()
        description = self.get_description() or ""
        for _api_name, _api_version in self.get_api_versions():
            description += f"\n({_api_name}, {_api_version})"
        for _k, _v in self.get_informational_key_value_pairs().items():
            description += f"\n{_k}={_v}"
        function_schemas = self.get_function_calling_function_schemas(datapoint)
        return FunctionNamespace(
            name=name,
            description=description,
            functions=function_schemas,
        )
    
    @classmethod
    def get_function_calling_function_schemas(cls, datapoint: HarmonyDatapoint | None = None) -> list[Function]:
        if datapoint.metadata.get(f"{cls.get_tool_name()}-functions_the_model_can_call") is not None:
            function_calls = datapoint.metadata[f"{cls.get_tool_name()}-functions_the_model_can_call"]
        else:
            function_calls = cls.get_names_of_functions_the_model_can_call()
        
        return [
            cls.get_function_calling_function_schema(name)
            for name in function_calls
        ]



# fix grep in command: 
# Hook that spots format checks inside each JSON object
def _reject_format(pairs):
    seen = {}
    for k, v in pairs:
        if k in seen:                       # ← duplicate!
            raise ValueError(f"duplicate key {k!r}")
        
        if k == 'command' and isinstance(v, str):
            if 'bash' in v:
                raise ValueError(f"forbidden command 'bash' in {k!r}; this function tool call already enforces 'bash' in execution.")
        seen[k] = v
    return seen


# Tool penalty configuration
TOOL_PENALTY_ANNEAL_STEPS = 5
TOOL_PENALTY_BASE = 0.02
def calculate_tool_penalty(message: chat.Message) -> float:
    """
    Computes an aux reward penalty for the **response** to a container.exec message.
    `exec_cmd`, `exec_status_code`, `exec_duration_seconds` are populated by CaasContainerTool.
    """

    unwanted_cmds_in_bash = ['sed', "oai", 'cat', 'bash', "apply-patch", "apply_patch", 'nano', 'head', 'tail']
    # Check if message is calling any of the bash execution tool variants (first tool in each variant set)
    bash_execution_tools = []
    for variant_set in BASH_VARIANTS:
        bash_execution_tools.append(f"functions.{variant_set[0]}")  # Only the first tool (execution tool)
    
    if message.recipient in bash_execution_tools:
        try:
            args = json.loads(str(message.content), object_pairs_hook=_reject_format)
            # Different bash variants use different parameter names for the command
            args_cmd = (args.get("command") or args.get("c") or args.get("cmd") or 
                       args.get("run") or args.get("cmd_line") or args.get("script") or 
                       args.get("instruction"))
            if args_cmd and 'bash' in args_cmd:
                return TOOL_PENALTY_BASE * 4
            if args_cmd and any(c in args_cmd for c in unwanted_cmds_in_bash):
                return TOOL_PENALTY_BASE * 3
        except Exception:
            return TOOL_PENALTY_BASE * 3

    error_messages = PADAWAN_TOOL_ERROR_MSGS
    if message.author.role == 'tool':
        response = render_content(message)
        if any(c in response for c in error_messages):
            return TOOL_PENALTY_BASE * 2
    
    tool_penalty: float = 0.0
    if (code := message.metadata.get("exec_status_code")) and code >= 124:
        # Timeout, not found, etc. usually indicate command misuse
        tool_penalty = TOOL_PENALTY_BASE
    if (duration := message.metadata.get("exec_duration_seconds")) and duration > 30:
        # Long commands come with a cost
        tool_penalty = TOOL_PENALTY_BASE

    return tool_penalty



@chz.chz(typecheck=True)
class MixToolConfig(OriginalCaasContainerToolConfig):
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown", "enabled"] = "no_access"

    tool_penalty_multiplier: float = 1.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 600_000

    def get_tool_name(self) -> str:
        return MixTool.get_tool_name()

    def unique_descriptor_for_variants(self) -> str:
        return MixTool.get_tool_name()

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = MixTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        return tool.instruction(datapoint)

    def get_names_of_functions_the_model_can_call(self) -> Collection[str]:
        return MixTool.get_names_of_functions_the_model_can_call()

    @property
    def _caas_resource_name(self) -> str:
        return _caas_container_resource_name(
            caas_network=self.caas_network,
            enable_network_after_setup=self.internet_policy == "enabled",
        )
    
    @final
    def initialize_tool(
        self, datapoint: HarmonyDatapoint | None = None, **kwargs: Any
    ) -> "BerryTool":
        # This method is final and cannot be overridden by subclasses
        tool = self._initialize_tool(datapoint=datapoint, **kwargs)
        tool_instruction = tool.instruction(datapoint)
        self_instruction = self.instruction(datapoint)
        if tool_instruction != self_instruction:
            diff = "\n".join(
                difflib.ndiff(tool_instruction.splitlines(), self_instruction.splitlines())
            )
            raise AssertionError(f"Instruction must match between tool and tool config:\n{diff}")
        assert tool.name == self.get_tool_name(), "Name must match between tool and tool config."
        tool._required_tools = self.required_tools
        return tool

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = MixTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool

