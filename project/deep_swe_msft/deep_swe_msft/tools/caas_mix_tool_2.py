import functools
import random
import json
import shlex
from typing import Any, AsyncIterator, Collection, Literal

import structlog
import orjson
import chat
import chz
from caas_tool.caas_container import <PERSON><PERSON>sContainer
from chat.render.common import render_content
from deep_swe.datasets.coreutils_setup_caas_plugin import CoreutilsSetupPluginConfig
from qstar.common import errors, types
from qstar.common.datapoint import HarmonyDatapoint
from berry_harmony.tools.berry_tool_interface import Berry<PERSON>ool
from berry_caas_container_tool.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from berry_caas_container_tool.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
    _caas_container_resource_name
)
from berry_caas_container_tool.caas_container_tool import (
    CaasToolPluginConfig,
    create_caas_tool_plugins,
)

from abc import abstractmethod
from typing import Annotated, AsyncIterator, ClassVar, Iterator, Sequence, Literal, Callable
from function_calling_tool import Function<PERSON><PERSON>ingTool, function_the_model_can_call

from deep_swe_msft.padawan_data.datapoint_converters import ENFORCE_COMMANDS_FORBIDDEN
from deep_swe_msft.tools.vscode_copilot_tool import exec as caas_exec
from deep_swe_msft.tools.utils import PADAWAN_TOOL_ERROR_MSGS

logger = structlog.stdlib.get_logger(component=__name__)

TOOL_MAXTOKENS = 4096

EDITOR_VARIANTS = ['str_replace_editor', 'text_file_editor', 'code_file_modifier', 'document_processor', 'file_handler', 'edit_tool', 'apply_patch']
BASH_VARIANTS = ['bash', 'run_in_terminal', 'shell_exec', 'terminal_cmd', 'cmd_runner']
REPORT_VARIANTS = ['report_progress']
VIEW_VARIANTS = ['grep_search', 'file_search', 'read_file', 'list_dir', 'semantic_search', 'text_search', 'find_files', 'view_content', 'explore_dir', 'code_search']

def _process_tool_message(message: str | chat.Message) -> str:
    """
    Truncate the message to a maximum of TOOL_MAXTOKENS tokens.
    """
    if isinstance(message, str):
        if len(message) > TOOL_MAXTOKENS:
            # Truncate the message content to TOOL_MAXTOKENS tokens
            message = message[:TOOL_MAXTOKENS] + " <reached max tokens>"
    else:
        if len(message.content.parts[0]) > TOOL_MAXTOKENS:
            # Truncate the message content to TOOL_MAXTOKENS tokens
            message.content.parts[0] = message.content.parts[0][:TOOL_MAXTOKENS] + " <reached max tokens>"
    return message

class MixTool(OriginalBerryCaasContainerTool):
    """
    Applies tool penalties and (forbidden) command enforcement!

    NOTE: Required command enforcement is done in IFEnforcementGrader;
    however failing forbidden commands immediately is more efficient and is a sort of weak credit assignment.
    """

    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        default_exec_timeout: int | None = 10_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        tool_set: str = 'default',
    ) -> None:
        super().__init__(
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty
        self.tool_set = tool_set

    # Remove feed_chars from the tool instructions
    @classmethod
    @functools.cache
    def get_names_of_functions_the_model_can_call(cls) -> Collection[str]:
        result = set(super().get_names_of_functions_the_model_can_call())
        result.discard("feed_chars")
        result.discard("exec")

        # Remove all variants from result first to avoid conflicts
        for variant in EDITOR_VARIANTS + BASH_VARIANTS + REPORT_VARIANTS + VIEW_VARIANTS:
            result.discard(variant)

        # Always ensure we have one tool from each essential category
        # Edit tool (required) - randomly select one variant
        selected_edit_tool = random.choice(EDITOR_VARIANTS)
        result.add(selected_edit_tool)
        
        # Bash tool (required) - randomly select one variant  
        selected_bash_tool = random.choice(BASH_VARIANTS)
        result.add(selected_bash_tool)
        
        # Report tool (required) - always add report_progress
        result.add('report_progress')
        
        # View tools (bonus) - add 1-10 random view tools for more capability
        num_view_tools = random.randint(1, 10)
        selected_view_tools = random.sample(VIEW_VARIANTS, min(num_view_tools, len(VIEW_VARIANTS)))
        for view_tool in selected_view_tools:
            result.add(view_tool)

        # # Randomly discard some of the remaining non-essential tools for variation
        # prob = random.random()
        # remaining_tools = [tool for tool in result if tool not in 
        #                   [selected_edit_tool, selected_bash_tool, 'report_progress'] + selected_view_tools]
        
        # if prob < 0.3:
        #     # Keep most tools (minimal discarding)
        #     tools_to_discard = random.sample(remaining_tools, min(len(remaining_tools) // 4, len(remaining_tools)))
        #     for tool in tools_to_discard:
        #         result.discard(tool)
        # elif prob < 0.6:
        #     # Moderate discarding
        #     tools_to_discard = random.sample(remaining_tools, min(len(remaining_tools) // 2, len(remaining_tools)))
        #     for tool in tools_to_discard:
        #         result.discard(tool)
        # else:
        #     # Aggressive discarding (keep only essentials + views)
        #     for tool in remaining_tools:
        #         result.discard(tool)

        # Optional shuffling for additional randomness
        if random.random() < 0.5:
            shuffle_result = list(result)
            random.shuffle(shuffle_result)
            result = set(shuffle_result)

        return result
    
    @classmethod
    def get_tool_name(cls) -> str:
        return "functions"
    
    @classmethod
    def get_description(cls) -> str | None:
        return 'Microsoft Padawan tool for code generation and editing'
    
    @function_the_model_can_call
    async def str_replace_editor(
        self,
        command: Annotated[
            Literal["view", "create", "str_replace", "insert", "undo_edit"],  
            "The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`."
        ],
        path: Annotated[
            str,  
            "Absolute path to file or directory."
        ],
        file_text: Annotated[
            str | None,  
            "Required parameter of `create` command, with the content of the file to be created."
        ] = None,
        insert_line: Annotated[
            int | None,  
            "Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`."
        ] = None,
        new_str: Annotated[
            str | None,  
            "Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert."
        ] = None,
        old_str: Annotated[
            str | None,  
            "Required parameter of `str_replace` command containing the string in `path` to replace. Leading and ending whitespaces from file content should be preserved!"
        ] = None,
        view_range: Annotated[
            list[int] | None,  
            "Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Custom editing tool for viewing, creating and editing files
        * State is persistent across command calls and discussions with the user
        * If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep
        * The `create` command cannot be used if the specified `path` already exists as a file
        * If a `command` generates a long output, it will be clipped in the middle and marked with `<file too long...`
        * The `undo_edit` command will revert the last edit made to the file at `path`
        Notes for using the `str_replace` command:
        * The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file.
        * If the `old_str` parameter is not unique in the file, the replacement will not be performed. Make sure to include enough context in `old_str` to make it unique
        * The `new_str` parameter should contain the edited lines that should replace the `old_str`
        """
        cmd = {"command": command, "path": path}
        
        if view_range is not None:
            cmd["view_range"] = view_range
            
        if file_text is not None:
            cmd["file_text"] = file_text
            
        if insert_line is not None:
            cmd["insert_line"] = insert_line
            
        if new_str is not None:
            cmd["new_str"] = new_str
            
        if old_str is not None:
            cmd["old_str"] = old_str

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def text_file_editor(
        self,
        op: Annotated[
            Literal["read", "write", "replace", "add", "revert"],  
            "Operation type. Options: `read`, `write`, `replace`, `add`, `revert`."
        ],
        p: Annotated[
            str,  
            "File or directory path (absolute)."
        ],
        txt: Annotated[
            str | None,  
            "Text content for `write` operation."
        ] = None,
        ln: Annotated[
            int | None,  
            "Line number for `add` operation. Text inserted after this line."
        ] = None,
        new: Annotated[
            str | None,  
            "New text for `replace` or content for `add` operation."
        ] = None,
        old: Annotated[
            str | None,  
            "Original text to replace in `replace` operation."
        ] = None,
        rng: Annotated[
            list[int] | None,  
            "Line range for `read` operation, e.g. [1, 10]."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Text file editing utility with compact interface
        * `read` shows file content or directory listing
        * `write` creates new file with provided content
        * `replace` substitutes old text with new text
        * `add` inserts content after specified line
        * `revert` undoes last modification
        """
        cmd_map = {"read": "view", "write": "create", "replace": "str_replace", "add": "insert", "revert": "undo_edit"}
        cmd = {"command": cmd_map[op], "path": p}
        
        if rng is not None:
            cmd["view_range"] = rng
        if txt is not None:
            cmd["file_text"] = txt
        if ln is not None:
            cmd["insert_line"] = ln
        if new is not None:
            cmd["new_str"] = new
        if old is not None:
            cmd["old_str"] = old

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def code_file_modifier(
        self,
        cmd: Annotated[
            Literal["show", "make", "edit", "append", "undo"],  
            "Command to execute: `show`, `make`, `edit`, `append`, `undo`."
        ],
        f: Annotated[
            str,  
            "Target file path."
        ],
        data: Annotated[
            str | None,  
            "File content for `make` command."
        ] = None,
        pos: Annotated[
            int | None,  
            "Position for `append` command."
        ] = None,
        upd: Annotated[
            str | None,  
            "Updated content for `edit` or `append` command."
        ] = None,
        orig: Annotated[
            str | None,  
            "Original content to modify in `edit` command."
        ] = None,
        span: Annotated[
            list[int] | None,  
            "Line span for `show` command."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Code modification tool with streamlined parameters
        * `show` displays file or directory contents
        * `make` creates new file with specified data
        * `edit` replaces original content with updated version
        * `append` adds content at specified position
        * `undo` reverses last change
        """
        cmd_map = {"show": "view", "make": "create", "edit": "str_replace", "append": "insert", "undo": "undo_edit"}
        command = {"command": cmd_map[cmd], "path": f}
        
        if span is not None:
            command["view_range"] = span
        if data is not None:
            command["file_text"] = data
        if pos is not None:
            command["insert_line"] = pos
        if upd is not None:
            command["new_str"] = upd
        if orig is not None:
            command["old_str"] = orig

        cmd_arguments = json.dumps(command)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def document_processor(
        self,
        action: Annotated[
            Literal["inspect", "generate", "modify", "inject", "rollback"],  
            "Action: `inspect`, `generate`, `modify`, `inject`, `rollback`."
        ],
        target: Annotated[
            str,  
            "Document path to process."
        ],
        content: Annotated[
            str | None,  
            "Document content for `generate` action."
        ] = None,
        line_pos: Annotated[
            int | None,  
            "Line position for `inject` action."
        ] = None,
        replacement: Annotated[
            str | None,  
            "Replacement text for `modify` or injection content for `inject`."
        ] = None,
        original: Annotated[
            str | None,  
            "Original text to find in `modify` action."
        ] = None,
        view_span: Annotated[
            list[int] | None,  
            "View range for `inspect` action."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Document processing interface with flexible operations
        * `inspect` examines document content
        * `generate` creates document with given content
        * `modify` changes original text to replacement
        * `inject` inserts content at line position
        * `rollback` undoes previous operation
        """
        action_map = {"inspect": "view", "generate": "create", "modify": "str_replace", "inject": "insert", "rollback": "undo_edit"}
        cmd = {"command": action_map[action], "path": target}
        
        if view_span is not None:
            cmd["view_range"] = view_span
        if content is not None:
            cmd["file_text"] = content
        if line_pos is not None:
            cmd["insert_line"] = line_pos
        if replacement is not None:
            cmd["new_str"] = replacement
        if original is not None:
            cmd["old_str"] = original

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def file_handler(
        self,
        mode: Annotated[
            Literal["peek", "build", "swap", "insert", "reset"],  
            "Mode of operation: `peek`, `build`, `swap`, `insert`, `reset`."
        ],
        loc: Annotated[
            str,  
            "File location."
        ],
        text: Annotated[
            str | None,  
            "Text for `build` mode."
        ] = None,
        idx: Annotated[
            int | None,  
            "Index for `insert` mode."
        ] = None,
        fresh: Annotated[
            str | None,  
            "Fresh content for `swap` or `insert` mode."
        ] = None,
        stale: Annotated[
            str | None,  
            "Stale content to replace in `swap` mode."
        ] = None,
        window: Annotated[
            list[int] | None,  
            "View window for `peek` mode."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Compact file handling tool
        * `peek` views file content within optional window
        * `build` constructs new file with text
        * `swap` exchanges stale content with fresh content
        * `insert` adds fresh content at index
        * `reset` undoes last modification
        """
        mode_map = {"peek": "view", "build": "create", "swap": "str_replace", "insert": "insert", "reset": "undo_edit"}
        cmd = {"command": mode_map[mode], "path": loc}
        
        if window is not None:
            cmd["view_range"] = window
        if text is not None:
            cmd["file_text"] = text
        if idx is not None:
            cmd["insert_line"] = idx
        if fresh is not None:
            cmd["new_str"] = fresh
        if stale is not None:
            cmd["old_str"] = stale

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def edit_tool(
        self,
        task: Annotated[
            Literal["display", "construct", "transform", "append", "revert"],  
            "Task type: `display`, `construct`, `transform`, `append`, `revert`."
        ],
        filepath: Annotated[
            str,  
            "Path to target file."
        ],
        body: Annotated[
            str | None,  
            "File body for `construct` task."
        ] = None,
        at: Annotated[
            int | None,  
            "Line position for `append` task."
        ] = None,
        to: Annotated[
            str | None,  
            "Target text for `transform` or content for `append`."
        ] = None,
        from_text: Annotated[
            str | None,  
            "Source text for `transform` task."
        ] = None,
        range_view: Annotated[
            list[int] | None,  
            "Range for `display` task."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Versatile editing tool with intuitive task names
        * `display` shows file content in specified range
        * `construct` builds new file with body content
        * `transform` changes from_text to target text
        * `append` adds content at specified line
        * `revert` undoes previous edit
        """
        task_map = {"display": "view", "construct": "create", "transform": "str_replace", "append": "insert", "revert": "undo_edit"}
        cmd = {"command": task_map[task], "path": filepath}
        
        if range_view is not None:
            cmd["view_range"] = range_view
        if body is not None:
            cmd["file_text"] = body
        if at is not None:
            cmd["insert_line"] = at
        if to is not None:
            cmd["new_str"] = to
        if from_text is not None:
            cmd["old_str"] = from_text

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["str_replace_editor", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def report_progress(
        self,
        commitMessage: Annotated[
            str,  
            "A short single line of text to use as the commit message"
        ],
        prDescription: Annotated[
            str,  
            "A description of work completed and remaining, using markdown checklists"
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        Report progress on the task. Call when you complete a meaningful unit of work. Commits and pushes changes that are pending in the repo, then updates the PR description.
        * Use this tool at least once, and as early as possible once you've established a plan. Outline the minimal-change plan as a checklist.
        * Use only when you have meaningful progress to report (you need to update the plan in the checklist, or you have completed a new item in the checklist)
        * Use markdown checklists to show progress (- [x] for completed items, - [ ] for pending items).
        * Keep the checklist structure as consistent as you can between updates, while still being accurate and useful.
        * Include 'Fixes #42.' as the last line of the body in the PR description.
        * Don't use headers in the PR description, just the checklist.
        * If there are changes in the repo this tool will run `git add .`, `git commit -m <msg>`, and `git push`.
        """
        cmd = {"commitMessage": commitMessage, "prDescription": prDescription}

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["report_progress", cmd_arguments]

        async for message in self.exec(cmd=container_cmd):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def bash(
        self,
        command: Annotated[
            str,  
            "The bash command to run."
        ],
        timeout: Annotated[
            int | None,
            "(Optional) Maximum time in seconds to wait for the command to complete. Default is 10 seconds if not provided."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Run commands in a bash shell
        * When invoking this tool, the contents of the 'command' parameter does NOT need to be XML-escaped.
        * You don't have access to the internet via this tool.
        * You can run Python, Node.js and Go code with the `python`, `node` and `go` commands.
        * You can install Linux, Python, JavaScript and Go packages with the `apt`, `pip`, `npm` and `go` commands.
        * This is a persistant bash session. State is saved across command calls and discussions with the user.
        * To inspect a particular line range of a file, e.g. lines 10-25, try 'sed -n 10,25p /path/to/the/file'.
        * Please avoid commands that may produce a very large amount of output.
        * Please run long lived commands in the background, e.g. 'sleep 10 &' or start a server in the background.
        * If a command times out, the session is reset and you'll need to reinitialize any environment state.
        """
        cmd = {"command": command}

        cmd_arguments = json.dumps(cmd)
        container_cmd = ["pdw_bash", cmd_arguments]

        async for message in self.exec(cmd=container_cmd, timeout=timeout):
            yield _process_tool_message(message)
    
    """
    VS Code Tools
    """
    @function_the_model_can_call
    async def grep_search(
        self,
        query: Annotated[
            str,
            "The pattern to search for in files in the workspace. Can be a regex or plain text pattern",
        ],
        isRegexp: Annotated[
            bool | None, "Whether the pattern is a regex. False by default."
        ] = None,
        includePattern: Annotated[
            str | None,
            "Search files matching this glob pattern. Will be applied to the relative path of files within the workspace.",
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Do a text search in the workspace. Use this tool when you know the exact string you're searching for.
        """
        cmd = {"query": query}

        if isRegexp is not None:
            cmd["isRegexp"] = isRegexp
        if includePattern is not None:
            cmd["includePattern"] = includePattern

        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="grep_search",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def apply_patch(
        self,
        input: Annotated[str, "The edit patch to apply."],
    ) -> AsyncIterator[chat.Message]:
        """
        Edit text files. Do not use this tool to edit Jupyter notebooks. `apply_patch` allows you to execute a diff/patch against a text file, but the format of the diff specification is unique to this task, so pay careful attention to these instructions. To use the `apply_patch` command, you should pass a message of the following structure as "input":

        *** Begin Patch
        [YOUR_PATCH]
        *** End Patch

        Where [YOUR_PATCH] is the actual content of your patch, specified in the following V4A diff format.

        *** [ACTION] File: [/absolute/path/to/file] -> ACTION can be one of Add, Update, or Delete.
        An example of a message that you might pass as "input" to this function, in order to apply a patch, is shown below.

        *** Begin Patch
        *** Update File: /Users/<USER>/pygorithm/searching/binary_search.py
        @@class BaseClass
        @@    def search():
        -        pass
        +        raise NotImplementedError()

        @@class Subclass
        @@    def search():
        -        pass
        +        raise NotImplementedError()

        *** End Patch
        Do not use line numbers in this diff format.
        """
        cmd = {"input": input}
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="apply_patch",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def file_search(
        self,
        query: Annotated[str, "Search for files with names or paths matching this glob pattern."],
        maxResults: Annotated[
            int | None,
            "The maximum number of results to return. Do not use this unless necessary, it can slow things down. By default, only some matches are returned. If you use this and don't see what you're looking for, you can try again with a more specific query or a larger maxResults.",
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Search for files in the workspace by glob pattern. This only returns the paths of matching files. Use this tool when you know the exact filename pattern of the files you're searching for. Glob patterns match from the root of the workspace folder. Examples:
        - **/*.{js,ts} to match all js/ts files in the workspace.
        - src/** to match all files under the top-level src folder.
        - **/foo/**/*.js to match all js files under any foo folder in the workspace.
        """
        cmd = {"query": query}
        if maxResults is not None:
            cmd["maxResults"] = maxResults
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="file_search",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def read_file(
        self,
        filePath: Annotated[str, "The absolute path of the file to read."],
        startLineNumberBaseZero: Annotated[int, "The line number to start reading from, 0-based."],
        endLineNumberBaseZero: Annotated[
            int, "The inclusive line number to end reading at, 0-based."
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        Read the contents of a file.

        You must specify the line range you're interested in. If the file contents returned are insufficient for your task, you may call this tool again to retrieve more content.
        """
        cmd = {
            "filePath": filePath,
            "startLineNumberBaseZero": startLineNumberBaseZero,
            "endLineNumberBaseZero": endLineNumberBaseZero,
        }
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="read_file",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def list_dir(
        self,
        path: Annotated[str, "The absolute path to the directory to list."],
    ) -> AsyncIterator[chat.Message]:
        """
        List the contents of a directory. Result will have the name of the child. If the name ends in /, it's a folder, otherwise a file
        """
        cmd = {"path": path}
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="list_dir",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def run_in_terminal(
        self,
        command: Annotated[str, "The command to run in the terminal."],
        explanation: Annotated[
            str,
            "A one-sentence description of what the command does. This will be shown to the user before the command is run.",
        ],
        isBackground: Annotated[
            bool,
            "Whether the command starts a background process. If true, the command will run in the background and you will not see the output. If false, the tool call will block on the command finishing, and then you will get the output. Examples of backgrond processes: building in watch mode, starting a server. You can check the output of a backgrond process later on by using get_terminal_output.",
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        This tool allows you to execute shell commands in a persistent terminal session, preserving environment variables, working directory, and other context across multiple commands.

        Command Execution:
        - Supports chaining with && or ; (e.g., npm install && npm start).
        - Supports multi-line commands

        Directory Management:
        - Use absolute paths to avoid navigation issues.

        Program Execution:
        - Supports Python, Node.js, and other executables.
        - Install dependencies via pip, npm, etc.

        Background Processes:
        - For long-running tasks (e.g., servers), set isBackground=true.
        - Returns a terminal ID for checking status and runtime later.

        Important Notes:
        - If the command may produce excessively large output, use head or tail to reduce the output.
        - If a command may use a pager, you must something to disable it. For example, you can use `git --no-pager`. Otherwise you should add something like ` | cat`. Examples: git, less, man, etc.
        - If a command times out, the session is reset and you'll need to reinitialize any environment state.
        """
        cmd = {
            "command": command,
            "explanation": explanation,
            "isBackground": isBackground,
        }

        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="run_in_terminal",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def semantic_search(
        self,
        query: Annotated[
            str,
            "The query to search the codebase for. Should contain all relevant context. Should ideally be text that might appear in the codebase, such as function names, variable names, or comments.",
        ],
    ) -> AsyncIterator[chat.Message]:
        """
        Run a natural language search for relevant code or documentation comments from the user's current workspace. Returns relevant code snippets from the user's current workspace if it is large, or the full contents of the workspace if it is small.
        """
        cmd = {"query": query}
        message, metadata = await caas_exec(
            self.container.caas_session, 
            tool_name="semantic_search", 
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    # @function_the_model_can_call
    # async def create_file(
    #     self,
    #     filePath: Annotated[str, "The absolute path to the file to create."],
    #     content: Annotated[str, "The content to write to the file."],
    # ) -> AsyncIterator[chat.Message]:
    #     """
    #     This is a tool for creating a new file in the workspace. The file will be created with the specified content.
    #     """
    #     cmd = {"filePath": filePath, "content": content}
    #     message, metadata = await caas_exec(
    #         self.container.caas_session, tool_name="create_file", tool_params=cmd
    #     )
    #     message = _process_tool_message(message)
    #     print(f"create_file: {message}")
    #     yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    # @function_the_model_can_call
    # async def get_errors(
    #     self,
    #     filePaths: Annotated[list[str], "The absolute path to the file to check for errors."],
    # ) -> AsyncIterator[chat.Message]:
    #     """
    #     Get any compile or lint errors in a code file. If the user mentions errors or problems in a file, they may be referring to these. Use the tool to see the same errors that the user is seeing. Also use this tool after editing a file to validate the change.
    #     """
    #     cmd = {"filePaths": filePaths}
    #     message, metadata = await caas_exec(
    #         self.container.caas_session, tool_name="get_errors", tool_params=cmd
    #     )
    #     message = _process_tool_message(message)
    #     print(f"get_errors: {message}")
    #     yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    # @function_the_model_can_call
    # async def test_search(
    #     self,
    #     filePaths: Annotated[
    #         list[str], "The absolute path to the file to check for test cases."
    #     ]
    # ) -> AsyncIterator[chat.Message]:
    #     """
    #     For a source code file, find the file that contains the tests. For a test file find the file that contains the code under test.
    #     """
    #     cmd = {"filePaths": filePaths}
    #     message, metadata = await caas_exec(
    #         self.container.caas_session, tool_name="test_search", tool_params=cmd
    #     )
    #     message = _process_tool_message(message)
    #     yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    # @function_the_model_can_call
    # async def search_workspace_symbols(
    #     self,
    #     symbolName: Annotated[
    #         str,
    #         "The symbol to search for, such as a function name, class name, or variable name.",
    #     ]
    # ) -> AsyncIterator[chat.Message]:
    #     """
    #     Search the user's workspace for code symbols using language services. Use this tool when the user is looking for a specific symbol in their workspace.
    #     """
    #     cmd = {"symbolName": symbolName}
    #     message, metadata = await caas_exec(
    #         self.container.caas_session, tool_name="search_workspace_symbols", tool_params=cmd
    #     )
    #     message = _process_tool_message(message)
    #     yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    # @function_the_model_can_call
    # async def list_code_usages(
    #     self,
    #     symbolName: Annotated[
    #         str,
    #         "The name of the symbol, such as a function name, class name, method name, variable name, etc.",
    #     ],
    #     filePaths: Annotated[
    #         list[str] | None,
    #         "One or more file paths which likely contain the definition of the symbol. For instance the file which declares a class or function. This is optional but will speed up the invocation of this tool and improve the quality of its output.",
    #     ] = None
    # ) -> AsyncIterator[chat.Message]:
    #     """
    #     Request to list all usages (references, definitions, implementations etc) of a function, class, method, variable etc. Use this tool when \n1. Looking for a sample implementation of an interface or class\n2. Checking how a function is used throughout the codebase.\n3. Including and updating all usages when changing a function, method, or constructor
    #     """
    #     cmd = {"symbolName": symbolName}
    #     if filePaths is not None:
    #         cmd["filePaths"] = filePaths
    #     message, metadata = await caas_exec(
    #         self.container.caas_session, tool_name="list_code_usages", tool_params=cmd
    #     )
    #     message = _process_tool_message(message)
    #     yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    # For applying penalties to bash command and enforcing forbidden commands

    # Bash tool variants
    @function_the_model_can_call
    async def shell_exec(
        self,
        cmd: Annotated[
            str,  
            "Shell command to execute."
        ],
        timeout_sec: Annotated[
            int | None,
            "Max execution time in seconds. Default 10s."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Execute shell commands with timeout control
        * Persistent session maintains state across calls
        * Supports Python, Node.js, Go execution
        * Package installation via apt, pip, npm, go
        * Background execution with & suffix
        * No internet access available
        """
        cmd_dict = {"command": cmd}
        cmd_arguments = json.dumps(cmd_dict)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=timeout_sec):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def terminal_cmd(
        self,
        execute: Annotated[
            str,  
            "Command to run in terminal."
        ],
        max_time: Annotated[
            int | None,
            "Maximum runtime in seconds."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Terminal command executor with persistent state
        * Maintains environment between executions
        * Supports multi-language runtime environments
        * Long-running processes should use background mode
        * File inspection via sed, head, tail recommended
        """
        cmd_dict = {"command": execute}
        cmd_arguments = json.dumps(cmd_dict)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=max_time):
            yield _process_tool_message(message)

    @function_the_model_can_call
    async def cmd_runner(
        self,
        run: Annotated[
            str,  
            "Command to run."
        ],
        wait: Annotated[
            int | None,
            "Wait timeout in seconds."
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Simple command runner interface
        * Stateful bash environment
        * Package management support
        * Background process execution
        * Output size management recommended
        """
        cmd_dict = {"command": run}
        cmd_arguments = json.dumps(cmd_dict)
        container_cmd = ["pdw_bash", cmd_arguments]
        async for message in self.exec(cmd=container_cmd, timeout=wait):
            yield _process_tool_message(message)

    # View tool variants
    @function_the_model_can_call
    async def text_search(
        self,
        pattern: Annotated[
            str,
            "Search pattern (text or regex)"
        ],
        regex: Annotated[
            bool | None, 
            "Pattern is regex. Default false."
        ] = None,
        files: Annotated[
            str | None,
            "File pattern to search within"
        ] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Search text patterns across workspace files
        * Supports both literal text and regex patterns
        * Can filter by file patterns using glob syntax
        * Returns matching lines with context
        """
        cmd = {"query": pattern}
        if regex is not None:
            cmd["isRegexp"] = regex
        if files is not None:
            cmd["includePattern"] = files
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="grep_search",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def find_files(
        self,
        name_pattern: Annotated[str, "File name pattern to search for"],
    ) -> AsyncIterator[chat.Message]:
        """
        Locate files by name pattern in workspace
        * Uses glob patterns for flexible matching
        * Searches entire workspace directory tree
        * Returns list of matching file paths
        """
        cmd = {"query": name_pattern}
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="file_search",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def view_content(
        self,
        path: Annotated[str, "File path to read"],
        start: Annotated[int, "Start line (0-based)"],
        end: Annotated[int, "End line (0-based, inclusive)"],
    ) -> AsyncIterator[chat.Message]:
        """
        Read file content within specified line range
        * Zero-based line numbering
        * Include end line in output
        * Call multiple times for different ranges if needed
        """
        cmd = {
            "filePath": path,
            "startLineNumberBaseZero": start,
            "endLineNumberBaseZero": end,
        }
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="read_file",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def explore_dir(
        self,
        dir_path: Annotated[str, "Directory path to explore"],
    ) -> AsyncIterator[chat.Message]:
        """
        Explore directory contents and structure
        * Lists files and subdirectories
        * Shows directory tree structure
        * Identifies file types by extension
        """
        cmd = {"path": dir_path}
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="list_dir",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    @function_the_model_can_call
    async def code_search(
        self,
        search_query: Annotated[str, "Semantic search query for code and documentation"],
    ) -> AsyncIterator[chat.Message]:
        """
        Intelligent code and documentation search
        * Semantic understanding of code concepts
        * Searches comments, documentation, and code
        * Returns contextually relevant matches
        """
        cmd = {"query": search_query}
        message, metadata = await caas_exec(
            self.container.caas_session,
            tool_name="semantic_search",
            tool_params=cmd,
            timeout=self._default_exec_timeout / 1000,
        )
        message = _process_tool_message(message)
        yield self.make_response(chat.Text.from_string(message), metadata=metadata)

    async def _process_sample_inner(
        self, sample: types.SampleWithCompletion[types.DatapointT]
    ) -> AsyncIterator[chat.Message]:

        # Check if the sample has a conversation and if the last message is a function call
        if forbidden_commands := sample.gt_datapoint.metadata.get(ENFORCE_COMMANDS_FORBIDDEN):
            try:
                assert sample.conversation
                if sample.conversation.messages[-1].recipient in [f"functions.{c}" for c in BASH_VARIANTS]:
                    args = json.loads(str(sample.conversation.messages[-1].content))
                    args_cmd = args.get("command")
                else:
                    args_cmd = None
            except Exception:
                args_cmd = ""

            if args_cmd:
                for forbidden_command in forbidden_commands:
                    if args_cmd.startswith(forbidden_command):
                        # The sample attempted to execute a forbidden command.
                        # Raise a ModelError to abort further processing.
                        raise errors.ModelError(
                            label="used_enforced_forbidden_command",
                            description="Attempted to call a forbidden tool command",
                            sample=sample,
                        )

        sample.metrics.setdefault("tool_penalty", 0.0)  # For correct wandb average
        async for message in super()._process_sample_inner(sample):
            yield message

            # Only apply tool penalties starting from step 10 onwards
            try:
                if (policy_step := sample.metadata.get("sampling/policy_step_initial")) and (
                    "exec_cmd" in message.metadata
                ):
                    # This message is the first **response** tool message
                    multiplier = min(1.0, policy_step / TOOL_PENALTY_ANNEAL_STEPS)
                    # (Penalty must be negative!)
                    penalty = (
                        multiplier * self.tool_penalty_multiplier * calculate_tool_penalty(message)
                    )
                    sample.metrics["tool_penalty"] -= penalty
                    if (
                        self.max_tool_penalty is not None
                        and sample.metrics["tool_penalty"] < -self.max_tool_penalty
                    ):
                        sample.metrics["tool_penalty"] = -self.max_tool_penalty
            except Exception:
                logger.exception("Error applying tool penalty", exc_info=True)

    def parse_function_call(self, message: chat.Message) -> tuple[str, Callable, dict[str, Any]]:
        """
        Parse this message as a call to one of the functions in this tool.

        Raises `chat.ToolInputError` if the message does not parse
        successfully, if the message has an unrecognized recipient, etc.
        """

        # Figure out which function the model is calling.
        recipient = message.recipient
        if "." not in recipient:
            raise chat.ToolInputError(
                f"Invalid {recipient=}, expected '{self.get_tool_name()}.<function_name>'"
            )
        tool_name, function_name = recipient.split(".", 1)
        if tool_name != self.get_tool_name():
            raise chat.ToolInputError(
                f"Wrong {tool_name=} (with {function_name=}), expected '{self.get_tool_name()}'"
            )
        fn = getattr(self.__class__, function_name, None)
        if fn is None:
            raise chat.ToolInputError(f"Unrecognized function name '{function_name}'")
        if not hasattr(fn, "__fn_calling_tool_fn_type__"):
            raise chat.ToolInputError(f"Model cannot call {function_name=}")
        json_string = chat.render.common.render_content(message, msg_idx=0)
        try:
            json.loads(json_string, object_pairs_hook=_reject_format)
        except ValueError as e:
            raise chat.ToolInputError(f"Could not parse args as JSON: {e}") from e
        try:
            kwargs = orjson.loads(json_string)
        except orjson.JSONDecodeError as e:
            raise chat.ToolInputError(f"Could not parse args as JSON: {e}") from e
        function_schema = self.get_function_calling_function_schema(function_name)
        assert function_schema is not None
        if not function_schema.validate_json(kwargs):
            raise chat.ToolInputError(
                f"Invalid {function_name=} call: {kwargs=}. Expected: {function_schema.to_typescript()}"
            )
        return function_name, fn, kwargs

# fix grep in command: 
# Hook that spots format checks inside each JSON object
def _reject_format(pairs):
    seen = {}
    for k, v in pairs:
        if k in seen:                       # ← duplicate!
            raise ValueError(f"duplicate key {k!r}")
        
        if k == 'command' and isinstance(v, str):
            if 'bash' in v:
                raise ValueError(f"forbidden command 'bash' in {k!r}; this function tool call already enforces 'bash' in execution.")
        seen[k] = v
    return seen


# Tool penalty configuration
TOOL_PENALTY_ANNEAL_STEPS = 5
TOOL_PENALTY_BASE = 0.02
def calculate_tool_penalty(message: chat.Message) -> float:
    """
    Computes an aux reward penalty for the **response** to a container.exec message.
    `exec_cmd`, `exec_status_code`, `exec_duration_seconds` are populated by CaasContainerTool.
    """

    unwanted_cmds_in_bash = ['sed', "oai", 'cat', 'bash', "apply-patch", "apply_patch"]
    if message.recipient in [f"functions.{c}" for c in BASH_VARIANTS]:
        try:
            args = json.loads(str(message.content), object_pairs_hook=_reject_format)
            args_cmd = args.get("command")
            if any(c in args_cmd for c in unwanted_cmds_in_bash):
                return TOOL_PENALTY_BASE * 2
        except Exception:
            return TOOL_PENALTY_BASE * 2

    error_messages = PADAWAN_TOOL_ERROR_MSGS
    if message.author.role == 'tool':
        response = render_content(message)
        if any(c in response for c in error_messages):
            return TOOL_PENALTY_BASE * 2
    
    tool_penalty: float = 0.0
    if (code := message.metadata.get("exec_status_code")) and code >= 124:
        # Timeout, not found, etc. usually indicate command misuse
        tool_penalty = TOOL_PENALTY_BASE
    if (duration := message.metadata.get("exec_duration_seconds")) and duration > 30:
        # Long commands come with a cost
        tool_penalty = TOOL_PENALTY_BASE

    return tool_penalty



@chz.chz(typecheck=True)
class MixToolConfig(OriginalCaasContainerToolConfig):
    exec_output_processor_path: str | None = (
        "teammate_service.post_processors.truncate:TruncateExecPostProcessor"
    )
    caas_container_image: str = "vscode-agent"
    terminal_emulator_path: str = "teammate_service.termites.lean_terminal:LeanTerminal"
    internet_policy: Literal["no_access", "only_http_get", "unknown", "enabled"] = "no_access"

    tool_penalty_multiplier: float = 2.0
    max_tool_penalty: float | None = 0.5

    caas_resource_name_override: str | None = chz.field(
        default=None,
        doc="if set, expect to be a str as caas_resource_name",
    )

    plugin_configs: tuple[CaasToolPluginConfig, ...] = chz.field(
        default=(CoreutilsSetupPluginConfig(),),
        doc="Plugin configurations for the tool",
    )

    tool_timeout: int = 1_980
    default_exec_timeout: int = 600_000

    def get_tool_name(self) -> str:
        return MixTool.get_tool_name()

    def unique_descriptor_for_variants(self) -> str:
        return MixTool.get_tool_name()

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = MixTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        return tool.instruction()

    def get_names_of_functions_the_model_can_call(self) -> Collection[str]:
        return MixTool.get_names_of_functions_the_model_can_call()

    @property
    def _caas_resource_name(self) -> str:
        return _caas_container_resource_name(
            caas_network=self.caas_network,
            enable_network_after_setup=self.internet_policy == "enabled",
        )

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = MixTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            default_exec_timeout=self.default_exec_timeout,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool

