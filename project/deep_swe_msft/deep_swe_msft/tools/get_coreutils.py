from datetime import datetime
import asyncio
import aiofiles
import json
import uuid
import traceback
import tenacity
from typing import Any
from functools import cache
import blobfile as bf
from caas.api import CaasSession
from caas.commands import Exec, RawExec, UploadFile, BashScript
from sciclone_utils.operations import sync_from_canonical_source

from deep_swe_msft.tools.vscode_copilot_tool import prepare_vsc_tool
from deep_swe_msft.tools.utils import MIX_VSC_TOOL, CRESCO_STORAGE_NAME

CAAS_ARTIFACTORY_NPM_REGISTRY = "https://npm.pkg.privatelink.caas.azure.com"

async def log_to_file(message: str, log_file: str = "/var/log/supervisor/padawan_setup.log"):
    """Logs a message to the specified log file with a timestamp."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    async with aiofiles.open(log_file, "a") as f:
        await f.write(f"[{timestamp}] {message}\n")

@cache
def get_padawan_tools_bytes(filename: str) -> bytes:
    uri = f"az://{CRESCO_STORAGE_NAME}/data/jadhuang/tools/padawan_tools/20250721/{filename}"
    return bf.read_bytes(sync_from_canonical_source(uri))


# oai-coreutils is built from monorepo: openai/project/teammate/teammate_coreutils
async def setup_coreutils(
    session: CaasSession,
    datapoint: dict[str, Any],
    repo_root: str | None,
    login: bool = False,
    install_node: bool = True,
    add_package_json: bool = True,
    mix_vsc_tool: bool = MIX_VSC_TOOL,
    skip_tool_packages: bool = False,
) -> None:

    try:
        await log_to_file("Starting setup_coreutils...\n")

        if mix_vsc_tool:
            await log_to_file(f"setup_vscutils starting with workdir: {repo_root}...")
            await prepare_vsc_tool(session, repo_root, language=None)
            await log_to_file("setup_vscutils done...")
            await log_to_file(f"setup_pdwutils starting with workdir: {repo_root}...")
            await prepare_padawan_tools(session, repo_root, install_node=False, skip_tool_packages=skip_tool_packages)
            await log_to_file("setup_pdwutils done...")
        else:
            await log_to_file(f"setup_pdwutils starting with workdir: {repo_root}...")
            await prepare_padawan_tools(session, repo_root, install_node=install_node, add_package_json=add_package_json, skip_tool_packages=skip_tool_packages)
            await log_to_file("setup_pdwutils done...")

        return 'Coreutils and Padawan tools setup completed successfully.'

    except Exception as e:
        await log_to_file(f"Error in setup_coreutils: {e}\n{traceback.format_exc()}")
        raise RuntimeError(
            f"Failed to install coreutils: {e}"
        )

TIMEOUT = 600


async def check_node_gyp_version(session, extra_log: str = ""):
    """ Check the version of node-gyp installed in the container. Some images
    were failing due to an incompatible version of node-gyp.
    """
    # Check npm's bundled node-gyp
    output = await session.run(Exec(["bash", "-c", "npm list node-gyp --depth=0 2>/dev/null || echo 'node-gyp not installed'"], timeout=60, workdir="/usr/local/bin/padawan_tools"))
    await log_to_file(f"{extra_log}Initial node-gyp status: {output.decode().strip()}\n")


async def prepare_padawan_tools(
    session: CaasSession,
    repo_root: str | None,
    install_node: bool = True,
    add_package_json: bool = False,
    skip_tool_packages: bool = False,
) -> None:
    try:
        await log_to_file("Starting setup_padawan...\n")
        if not skip_tool_packages:
            if install_node:
                TMP_DRI="/usr/local/nodejsinstall"
                NODEJS_VERSION='22.14.0'
                blob_name = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"
                await session.run(Exec(["bash", "-c", f"set -x && time tar -xf {blob_name} -C /usr/local"], timeout=120, workdir=TMP_DRI))
                node_version_output = await session.run(Exec(["bash", "-c", f"set -x && node -v && npm -v"], workdir="/"))
                await log_to_file(f"node version output (install_node=True): {node_version_output.decode().strip()}\n")
            else:
                node_version_output = await session.run(Exec(["bash", "-c", f"set -x && node -v && npm -v"], workdir="/"))
                await log_to_file(f"node version output (install_node=False): {node_version_output.decode().strip()}\n")
            # Upload padawan_tools to the container
            await session.run(Exec(['bash', '-lc', 'mkdir -p /usr/local/bin/padawan_tools'], timeout=TIMEOUT, workdir='/',env=None))
            indexjs = get_padawan_tools_bytes(filename="index.js")
            await session.run(UploadFile('/usr/local/bin/padawan_tools/index.js', indexjs))
            toolwrapperjs = get_padawan_tools_bytes(filename="tool_wrapper.js")
            await session.run(UploadFile('/usr/local/bin/padawan_tools/tool_wrapper.js', toolwrapperjs))
            daemon_managerjs = get_padawan_tools_bytes(filename="daemon-manager.js")
            await session.run(UploadFile('/usr/local/bin/padawan_tools/daemon-manager.js', daemon_managerjs))
            bash_daemonjs = get_padawan_tools_bytes(filename="bash-daemon.js")
            await session.run(UploadFile('/usr/local/bin/padawan_tools/bash-daemon.js', bash_daemonjs))
            package_json = get_padawan_tools_bytes(filename="package.json")
            await session.run(UploadFile('/usr/local/bin/padawan_tools/package.json', package_json))

            # Install node-pty
            await session.run(Exec(["bash", "-c", f"npm config set registry {CAAS_ARTIFACTORY_NPM_REGISTRY}"]))

            if install_node:
                # Attempt to install node-gyp
                await check_node_gyp_version(session, extra_log="Before: ")
                output = await session.run(Exec([
                    'bash',
                    '-c',
                    'touch /usr/local/bin/padawan_tools/npm_install_node_gyp.log'
                ], timeout=120, workdir='/usr/local/bin/padawan_tools',))
                await session.run(Exec(["bash", "-c", "npm install node-gyp@10.0.1 --verbose 2>&1 | tee /usr/local/bin/padawan_tools/npm_install_node_gyp.log"],
                    timeout=120, workdir='/usr/local/bin/padawan_tools', detach=True,
                    env={"NODE_EXTRA_CA_CERTS": "/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt"})
                )
                # Run a command to test pty
                for _ in range(TIMEOUT // 20):
                    await asyncio.sleep(20)
                    output = await session.run(RawExec(['bash', '-c', 'cat /usr/local/bin/padawan_tools/npm_install_node_gyp.log'],
                                                    timeout=TIMEOUT,
                                                    workdir='/usr/local/bin/padawan_tools'))
                    if output[0] == 0 and "npm verbose exit 0" in output[1].decode():
                        await log_to_file(f"[DEBUG] DONE: {output[1].decode()}")
                        break
                await check_node_gyp_version(session, extra_log="After: ")
            output = await session.run(Exec([
                'bash',
                '-c',
                'touch /usr/local/bin/padawan_tools/npm_install_node_pty.log'
            ], timeout=120, workdir='/usr/local/bin/padawan_tools',))
            await session.run(
                Exec(
                    ['bash', '-c', 'npm i node-pty@1.1.0-beta33 --verbose 2>&1 | tee /usr/local/bin/padawan_tools/npm_install_node_pty.log'],
                    timeout=120, workdir='/usr/local/bin/padawan_tools', detach=True,
                    env={"NODE_EXTRA_CA_CERTS": "/usr/local/share/ca-certificates/mitmproxy-ca-cert.crt"}
                )
            )
            # Run a command to test pty
            for _ in range(TIMEOUT // 20):
                await asyncio.sleep(20)
                output = await session.run(RawExec(['bash', '-c', 'node -e "const pty = require(\'node-pty\'); console.log(\'node-pty is working\');"'],
                                                timeout=TIMEOUT,
                                                workdir='/usr/local/bin/padawan_tools'))
                if output[0] == 0:
                    await log_to_file(f"[DEBUG] DONE: {output[1].decode()}")
                    break
            if output[0] != 0:
                await log_to_file(f"[DEBUG] Failed to install node-pty: {output[1].decode()}")
                output = await session.run(Exec(['bash', '-c', 'cat /usr/local/bin/padawan_tools/npm_install_node_pty.log']))
                await log_to_file(f"[DEBUG] Failure log: {output.decode()}")
                raise RuntimeError("Failed to install node-pty")
        
        # Start the padawan daemon process -> detached to background
        await session.run(Exec(['bash', '-c', 'node daemon-manager.js start'], timeout=TIMEOUT, workdir='/usr/local/bin/padawan_tools', detach=True))

        # Check if git is initialized and handle ownership issues
        exit_code, output = await session.run(RawExec(['git', 'status'], timeout=TIMEOUT, workdir=repo_root, env=None))
        
        if exit_code != 0:
            output_str = output.decode()
            if 'fatal: not a git repository' in output_str:
                # Initialize git repository
                git_setup = [
                    "git init",
                    "git config --global user.name User",
                    "git config --global user.email user@localhost",
                ]
                await session.run(BashScript("\n".join(git_setup), timeout=TIMEOUT, workdir=repo_root, env=None, login=False))
        
        # Add repository to safe directories and retry
        await session.run(RawExec(['git', 'config', '--global', '--add', 'safe.directory', repo_root], timeout=TIMEOUT, workdir=repo_root, env=None))

        # Create a new branch for model to use
        branchName = f'model-branch-{uuid.uuid4().hex[:8]}'  # Unique branch name

        # Force create/switch to the branch (handles existing branch case)
        await session.run(RawExec(['bash', '-c', f'git checkout -B {branchName}'], timeout=TIMEOUT, workdir=repo_root, env=None))
        await session.run(RawExec(['bash', '-c', f'git add .'], timeout=TIMEOUT, workdir=repo_root, env=None))
        await session.run(RawExec(['bash', '-c', f'git commit -m "New Initial Commit"'], timeout=TIMEOUT, workdir=repo_root, env=None))

        mock_config = {'location': repo_root, 'branchName': branchName}
        mock_config_json = json.dumps(mock_config)
        await log_to_file(f"{mock_config_json}\n")

        if add_package_json:
            # This is to avoid: SyntaxError: Cannot use import statement outside a module
            mock_package_json = {"type": "module"}
            await session.run(
                UploadFile("/usr/local/bin/package.json", json.dumps(mock_package_json).encode("utf-8"))
            )

        # Make the tool executable
        cmd_str_replace = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js str_replace_editor '{mock_config_json}' "$@"'''
        await session.run(UploadFile('/usr/local/bin/str_replace_editor', cmd_str_replace.encode('utf-8')))
        await session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/str_replace_editor'], timeout=TIMEOUT, workdir='/', env=None))

        cmd_report_progress = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js report_progress '{mock_config_json}' "$@"'''
        await session.run(UploadFile('/usr/local/bin/report_progress', cmd_report_progress.encode('utf-8')))
        await session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/report_progress'], timeout=TIMEOUT, workdir='/', env=None))

        cmd_bash = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js bash '{mock_config_json}' "$@"'''
        await session.run(UploadFile('/usr/local/bin/pdw_bash', cmd_bash.encode('utf-8')))
        await session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/pdw_bash'], timeout=TIMEOUT, workdir='/', env=None))

        cmd_bash = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js read_bash '{mock_config_json}' "$@"'''
        await session.run(UploadFile('/usr/local/bin/pdw_read_bash', cmd_bash.encode('utf-8')))
        await session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/pdw_read_bash'], timeout=TIMEOUT, workdir='/', env=None))

        cmd_bash = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js write_bash '{mock_config_json}' "$@"'''
        await session.run(UploadFile('/usr/local/bin/pdw_write_bash', cmd_bash.encode('utf-8')))
        await session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/pdw_write_bash'], timeout=TIMEOUT, workdir='/', env=None))

        cmd_bash = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js stop_bash '{mock_config_json}' "$@"'''
        await session.run(UploadFile('/usr/local/bin/pdw_stop_bash', cmd_bash.encode('utf-8')))
        await session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/pdw_stop_bash'], timeout=TIMEOUT, workdir='/', env=None))

    except Exception as e:
        await log_to_file(f"Error in setup_padawan for {repo_root}: {e}\n{traceback.format_exc()}")
        await session.close()
        raise RuntimeError(
            f"Failed to install padawan: {e}"
        )
