dt=`date '+%Y%m%d-%H%M%S'`
# EXPERIMENT_NAME="mix16-arm-1-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run2-resumeprod-run2"
EXPERIMENT_NAME="mix16-arm-1-pdw2-ev3-sbhv2-derisk-$dt"
# Enforcement settings for datasets 0-6
ENFORCE_PROB=0.3
ENFORCE_FREQ=5

CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict
name=$EXPERIMENT_NAME
# name=pdw2-test-sbh-run1
seed=20250501

skip_validate_config=True

# Policy settings
:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
# :berry_models.scallion_lpe:d36_80g_mbg16_bf16
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
# policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True'

# nv4
# policy.initial_checkpoint=az://orngscuscresco/models/snapshots/bmckinzie-nv4-25T-mident-ipb512-spi128-tbv2-run1_step500-retransfer-decrypted/
# o4-mini
# policy.initial_checkpoint=az://orngscuscresco/models/snapshots/oseries-nv4-cbv5-0331-step350-decrypted/
# codex-mini
# policy.initial_checkpoint=az://orngscuscresco/models/nv4-codex-tc-may11-s200-decrypted
# mix 15
# policy.initial_checkpoint="az://orngscuscresco/twapi/mini/e/qingruzhang-swe_workflow_sft_after_mix15_run7_s480_lr8e-5-run3/fd03e8bc-deb1-4846-86fc-78087ebb3956/checkpoint/model1/000000000160/"
# policy.initial_checkpoint="az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-1-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run2-resumeprod-20250724-071110/policy/step_000040/"
# policy.initial_checkpoint="az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-1-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-resumed-run-20250725-020418/policy/step_000070/"
# policy.initial_checkpoint="az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-1-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-resumed-run-20250725-190838/policy/step_000075/"
# policy.initial_checkpoint="az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-1-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-resumed-run-20250726-001426/policy/step_000115/"
policy.initial_checkpoint="az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-1-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr2e-6-resumed-run-20250727-162422/policy/step_000325/"
policy.initial_checkpoint_mode=resume
policy.n_gpus=128
policy.is_multimodal=True
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.sampling_use_ev3=True
peashooter.timeout_seconds.stalled_datapoint=3600
timeout.default=None

# Model settings
policy.n_ctx=131072
defaults.n_ctx=131072
...container_tool_config="mix_tool"
...sampler='deep_swe_msft.padawan_data.sampler:PadawanSampler'
...harmony_constrained_sampling=True
# defaults.sample_completer="deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter"
optimizer.hparam_scaler.lr_per_instance_d16=2e-6
...save_every=5

# Dataset configs!
:deep_swe_msft.presets:train_padawan_v2_mix16_arm_1_sbhv2
...dataset_container=orngscuscresco
...max_num_yields=256
...override_pass_rate_minimum=0.01

berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=10

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=1024
defaults.instances_per_batch=32
defaults.target_samples_per_instance=32

batch_completer.n_batches_in_flight=30 # number of batches in flight
peashooter.num_sampling_processes=32 # number of sampling processes per instance worker
peashooter.sampling_concurrency=16 # concurrency sampling threads per process
peashooter.num_instance_workers=64 # number of instance workers

# Dataset configs
defaults.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer

# swe-bench-hard repair
batcher.curriculum.training_datasets.0.dataset.inverse_token_cost_multiplier=16
batcher.curriculum.training_datasets.0.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
batcher.curriculum.training_datasets.0.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
batcher.curriculum.training_datasets.0.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ

# swe-bench-train
# batcher.curriculum.training_datasets.1.dataset.instance_completer="deep_swe_msft.swe_bench_train_v2_padawan_v2.dataset_config:SWEBenchV2InstanceCompleter"
# batcher.curriculum.training_datasets.1.dataset.inverse_token_cost_multiplier=16
# batcher.curriculum.training_datasets.1.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
# batcher.curriculum.training_datasets.1.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
# batcher.curriculum.training_datasets.1.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ
# # batcher.curriculum.training_datasets.0.dataset.override_target_samples_per_instance=128

# webdev task
# batcher.curriculum.training_datasets.2.dataset.inverse_token_cost_multiplier=16
# batcher.curriculum.training_datasets.3.dataset.inverse_token_cost_multiplier=16

# # swe-bench-hard repair rewrite 1x
# batcher.curriculum.training_datasets.21.dataset.inverse_token_cost_multiplier=32

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-main-run
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a swe-nv4.log
