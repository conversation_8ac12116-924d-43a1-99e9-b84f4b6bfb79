from codex_msft.common.prompt import get_padawan_prompt

# storage config ---------------------------------------------
STORAGE_ACCOUNT = "orngscuscresco"  # The orange storage account, remember to set env variable STORAGE_ACCOUNT="orngcresco" if your devbox is in uksouth region


# bus config -------------------------------------------------
BUS_USER = "evaluation"
BUS_TOPIC = "az://orngcresco/twapi/mini/e/zhendongwang-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run2-continue2/policy/step_000120"
BUS_RENDER = "harmony_v4.0.16_berry_v3_1mil_orion_mm_except_tools_no_budget"

# prompt config -----------------------------------------------
SYSTEM_PROMPT = get_padawan_prompt(
    reasoning=False
)  # if reasoning is True, it will activate the "think" tool

# caas config -------------------------------------------------
CAAS_IMAGE = "aio"  # The image url to use for the caas container
REPO_ROOT = "/"  # The working directory of caas container

# CAAS_IMAGE = "acrbuiltincaasglobalame.azurecr.io/caas-swe-bench:20250115a-matplotlib__matplotlib-13989"
# REPO_ROOT = "/testbed"
