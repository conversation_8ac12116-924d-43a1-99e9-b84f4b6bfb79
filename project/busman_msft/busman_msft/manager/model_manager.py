"""Unified Model Manager for loading and managing models from different sources."""

import logging
from typing import Dict, List, Optional

from model_registry.model_data import BaseModelData
from omom_msft.models import ModelConfig

from ..loader.unified_model_loader import load_all_models

logger = logging.getLogger(__name__)


class ModelManager:
    """Unified model manager for loading and managing models from different sources."""

    def __init__(
        self,
        enable_deduplication: bool = True,
        priority_source: str = "model_registry",
        auto_load: bool = True,
    ):
        """Initialize the model manager.

        Args:
            enable_deduplication: Whether to remove duplicate models when loading
            priority_source: Which source to prioritize when deduplicating ("model_registry" or "omom")
            auto_load: Whether to automatically load models during initialization
        """
        self.enable_deduplication = enable_deduplication
        self.priority_source = priority_source
        self._models: List[BaseModelData] = []
        self._models_by_name: Dict[str, BaseModelData] = {}
        self._loaded = False

        if auto_load:
            self.load_models()

    def load_models(self, force_reload: bool = False) -> None:
        """Load all models from available sources.

        Args:
            force_reload: Whether to force reload even if models are already loaded
        """
        if self._loaded and not force_reload:
            logger.info("Models already loaded. Use force_reload=True to reload.")
            return

        logger.info("Loading models from all sources...")
        try:
            self._models = load_all_models(
                enable_deduplication=self.enable_deduplication,
                priority_source=self.priority_source,
            )
            self._build_name_index()
            self._loaded = True
            logger.info(f"Successfully loaded {len(self._models)} models")
        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            raise

    def _build_name_index(self) -> None:
        """Build an index of models by name for fast lookup."""
        self._models_by_name = {}
        for model in self._models:
            name = getattr(model, "name", None)
            if name:
                self._models_by_name[name] = model

    def get_model_by_name(self, name: str) -> Optional[BaseModelData]:
        """Get a model by its name.

        Args:
            name: The name of the model to retrieve

        Returns:
            BaseModelData object if found, None otherwise
        """
        if not self._loaded:
            logger.warning("Models not loaded. Loading now...")
            self.load_models()

        return self._models_by_name.get(name)

    def list_models_names(self) -> List[str]:
        """Get a list of all available model names.

        Returns:
            List of model names
        """
        if not self._loaded:
            logger.warning("Models not loaded. Loading now...")
            self.load_models()

        return list(self._models_by_name.keys())

    def get_all_models(self) -> List[BaseModelData]:
        """Get all loaded models.

        Returns:
            List of all BaseModelData objects
        """
        if not self._loaded:
            logger.warning("Models not loaded. Loading now...")
            self.load_models()

        return self._models.copy()
