"""Engine configuration component for managing engine settings."""

import streamlit as st
from busman_msft.cluster.available_clusters import Cluster
from busman_msft.service.engine_service import EngineService
from omom_msft.manager import <PERSON><PERSON>anager as OMOMModelManager
from omom_msft.models import ModelConfig


def render(engine_service: EngineService, model_name: str, omom_model_manager: OMOMModelManager):
    """Render the engine configuration section.

    Args:
        model: ModelConfig instance containing model information
        manager: ModelManager instance for command generation
    """
    if not model_name:
        st.error("No model_name provided.")
        return

    st.write("### Start Engine")

    with st.form(key=f"config_{model_name}"):
        col_gpu, col_replicas, col_region, col_engine = st.columns(4)

        with col_gpu:
            gpu_type = st.selectbox("GPU Type", ["A100_80G", "V100_32G"], index=0)

        with col_replicas:
            n_replicas = st.number_input("Number of Replicas", min_value=1, max_value=10, value=1)

        with col_region:
            cluster = st.selectbox("Region", [c.name for c in Cluster], index=0)

        with col_engine:
            engine_type = st.selectbox("Engine Type", ["bus", "twapi"], index=0)

        if st.form_submit_button("Show Command"):
            command = omom_model_manager.generate_cmd_command(
                model_name,
                gpu_kind=gpu_type,
                n_replicas=n_replicas,
                engine_type=engine_type,
                region=Cluster.of(cluster).region,
            )
            with st.expander("Run this command to start engine", expanded=True):
                st.code(command, language="bash")
        if st.form_submit_button("Start Engine from Local"):
            st.info("Starting engine... Please see console for logs.")
            engine_service.start(model_name, gpu_type, n_replicas, Cluster.of(cluster))
