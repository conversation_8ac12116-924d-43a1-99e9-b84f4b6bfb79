"""Model detail component for displaying model information."""

import streamlit as st
from model_registry.model_data import BaseModelData


def render(model: BaseModelData):
    """Render the model details section.

    Args:
        model: BaseModelData instance containing model information
    """
    if not model:
        st.error("No model provided.")
        return

    # All models should now be BaseModelData instances
    if not isinstance(model, BaseModelData):
        st.error(f"Invalid model type: {type(model)}. Expected BaseModelData.")
        return

    # Render the unified model data
    render_model_data(model)


def render_model_data(model: BaseModelData):
    """Render unified model data details.

    Args:
        model: BerryModelData or BaseModelData instance containing model information
    """
    st.write(f"## {getattr(model, 'name', 'Unknown Model')}")

    st.write("### Model Configuration")
    col_basic_info, col_advanced_info = st.columns(2)

    with col_basic_info:
        st.write("**Model Name**")
        st.caption(model.name or "N/A")

        st.write("**External Name**")
        st.caption(model.external_name or "N/A")

        st.write("**Snapshot Path**")
        st.caption(model.snapshot_path or "N/A")

        st.write("**Renderer**")
        st.caption(model.renderer_name or "N/A")

        st.write("**Type**")
        st.caption("Multimodal" if model.features.is_multimodal else "Text-only")

        st.write("**Source**")
        st.caption(model._source or "N/A")

    with col_advanced_info:
        # Extra args
        extra_args = getattr(model, "extra_args", [])
        st.write("**Extra Arguments**")
        with st.expander(f"{len(extra_args)} items", expanded=False):
            if extra_args:
                for arg in extra_args:
                    st.caption(f"• {arg}")
            else:
                st.caption("No extra arguments")

        # Extra kwargs
        extra_kwargs = getattr(model, "extra_kwargs", {})
        st.write("**Extra Kwargs**")
        with st.expander(f"{len(extra_kwargs)} items", expanded=False):
            if extra_kwargs:
                for key, value in extra_kwargs.items():
                    st.caption(f"• **{key}**: {value}")
            else:
                st.caption("No extra kwargs")

        # Features (if available)
        features = getattr(model, "features", None)
        if features:
            st.write("**Model Features**")
            with st.expander("Features", expanded=False):
                if hasattr(features, "is_multimodal"):
                    st.caption(f"• **Multimodal**: {features.is_multimodal}")
                if hasattr(features, "encoder_decoder_snapshots"):
                    st.caption(f"• **Encoder-Decoder**: {features.encoder_decoder_snapshots}")
                # render other features
                for attr in dir(features):
                    if not attr.startswith("_") and attr not in [
                        "is_multimodal",
                        "encoder_decoder_snapshots",
                    ]:
                        value = getattr(features, attr)
                        if not callable(value):
                            st.caption(f"• **{attr}**: {value}")
