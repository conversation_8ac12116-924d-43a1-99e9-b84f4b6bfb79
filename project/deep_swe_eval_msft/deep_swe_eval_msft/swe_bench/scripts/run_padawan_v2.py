from chat.render.renderer_registry import get_renderer
from chat import chat
from chat.render.common import render_content
from legacy_rest_token_completer import LegacyRest<PERSON>okenCompleter
from message_completer.message_completer import MessageCompleter
from message_completer.token_message_completer import TokenMessageCompleter
from token_completer.token_completer import TokenCompleter
from turn_completer import SingleMessageTurnCompleter
from token_completer.backend import CompleterBackend
from smokey import Smokey
from bus_token_completer import BusTokenCompleter
import shlex
from caas_swe_bench_train_v2.parser import ModelPatch
from caas_utils.utils import run_with_retries
from uuid import uuid4

from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from chat import Conversation, Role
import chat
from chat.tools import DefaultMultiToolKit, take_one_step_with_tools

import json
import os, random
import asyncio
from typing import Callable, Coroutine
from functools import cache
from collections import defaultdict
import copy
import time, re
from datetime import datetime
from caas.api import caas_api
from caas.protocol import NetworkMode, SandboxRuntime
from caas.terminal.api import TerminalSession

from message_completer.message_completer import ParseErrorMode

from caas_tool.caas_container_tool import CaasContainerTool
from caas_tool.caas_container import CaasContainer
# from caas_tool.caas_container import CAAS_ENDPOINT
from caas.commands import DownloadFileFromContainer, Exec, PythonProgram, UploadFile, RawExec
from tool_use_free.tool_registry import create_toolkit
from caas.commands import BashScript
from chat import (
    Conversation,
    ConversationMetadata,
    Message,
    chat,
)
from chat import Role
from message_completer import TokenMessageCompleter
from token_completer import CompleterBackend, TokenCompleter
from token_completer.retry_utils import NoRetryConfig
import chz
from typing import Any, Unpack, cast
from harmony_gym.env.convo_roller import ToolConvoRoller
# from harmony_gym.reward.msft.caas_tool import CaasToolReward
from legacy_rest_token_completer import LegacyRestTokenCompleter
from harmony_gym.utils.types import (
    LoggingResultQueue,
    RewardResultQueue,
    RolloutEnvidFinishQueue,
    RolloutResultQueue,
    HarmonyRewardResultsForSamePrefixConvo,
    HarmonyRolloutPerStepResult,
    HarmonyRolloutResult,
    HarmonyRolloutResultsForSamePrefixConvo,
    RolloutVariant,
)
from chat.tools import DefaultMultiToolKit
from multimodal_token import TokList
from contextlib import closing

import numpy as np
from pathlib import Path
from datasets import load_dataset, load_from_disk
from deep_swe_eval_msft.swe_bench.preparedness.swebench_testspec import make_test_spec
from swebench.harness.grading import get_eval_report_hack
from caas.protocol import NetworkMode
#from msft_prompts.padawan_v2_prompt import (
from deep_swe_eval_msft.data_converter.padawan_v2_prompt import (
    systemMessage,
    get_user_message,
    get_summarizingSystemMessage,
    get_prDescriptionUserMessage
)
from deep_swe_msft.padawan_data.system_prompt import PADAWAN_MODEL_IDENTITY, PADAWAN_SYSTEM_PROMPT
# from deep_swe_eval_msft.swe_bench.preparedness.swebench_testspec import make_test_spec
# from deep_swe_eval_msft.swe_bench.harness.grading import get_eval_report_hack
# from caas.protocol import NetworkMode
# from deep_swe_eval_msft.data_converter.padawan_v2_prompt import (
#     systemMessage,
#     get_user_message,
#     get_summarizingSystemMessage,
#     get_prDescriptionUserMessage
# )

from caas.protocol import VolumeMount
# from tools.caas_padawan_tool import DeepSWECaasPadawanTool
from deep_swe_msft.tools.caas_padawan_tool import DeepSWECaasPadawanTool


USE_SWE_GRADER = True
USE_SWE_WORKER = False
USE_BUS = False

def write_messages_to_file(messages, file_name = "aaa", folder = "log"):
    if not isinstance(messages, list):
        messages = [messages]
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, file_name), "w") as f:
        for m in messages:
            f.write(m.json() + "\n")

def write_to_file_text(text, file_name = "aaa", folder = "log"):
    os.makedirs(folder, exist_ok=True)
    with open(os.path.join(folder, file_name), "w") as f:
        f.write(text)

oai_excludes = [
    "django__django-7530",
    "sphinx-doc__sphinx-9367",
    "matplotlib__matplotlib-26342",
    "astropy__astropy-8707",
    "astropy__astropy-8872",
    "astropy__astropy-7606",
    "pylint-dev__pylint-7277",
    "sphinx-doc__sphinx-7462",
    "psf__requests-6028",
    "scikit-learn__scikit-learn-12973",
    "matplotlib__matplotlib-20676",
    "sphinx-doc__sphinx-8265",
    "matplotlib__matplotlib-24970",
    "matplotlib__matplotlib-23299",
    "matplotlib__matplotlib-20488",
    "pylint-dev__pylint-6528",
    "matplotlib__matplotlib-20826",
    "django__django-10097",
    "sphinx-doc__sphinx-10466",
    "pytest-dev__pytest-7521",
    "pytest-dev__pytest-5262",
    "matplotlib__matplotlib-25479",
    "pylint-dev__pylint-7080"
]

claude_excludes = [
    "scikit-learn__scikit-learn-14710",
    "django__django-10097",
    "psf__requests-2317",
    "sphinx-doc__sphinx-10435",
    "sphinx-doc__sphinx-7985",
    "sphinx-doc__sphinx-8475",
    "matplotlib__matplotlib-20488",
    "astropy__astropy-8707",
    "astropy__astropy-8872",
    "sphinx-doc__sphinx-8595",
    "sphinx-doc__sphinx-9711"
]

msft_excludes = [
    'astropy__astropy-8707',
    'astropy__astropy-8872',
    'astropy__astropy-7606',
    'django__django-10097',
    'matplotlib__matplotlib-20488',
    'sphinx-doc__sphinx-10435',
    'sphinx-doc__sphinx-10323',
    'psf__requests-2317',
    # additional excludes
    "sphinx-doc__sphinx-7985",
    "matplotlib__matplotlib-23299",
]


padawan_tool_instruction = """
// Microsoft Padawan tool for code generation and editing
// (container_tool, 1.2.0)
// (lean_terminal, 1.0.0)
// (caas, 2.3.0)
namespace functions {

// Runs a bash command in an interactive bash session.
// * When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.
// * You don't have access to the internet via this tool.
// * You can run Python, Node.js and Go code with the `python`, `node` and `go` commands.
// * You can install Linux, Python, JavaScript and Go packages with the `apt`, `pip`, `npm` and `go` commands.
// * Each sessionId identifies a persistent bash session. State is saved across command calls and discussions with the user.
// * `timeout` parameter must be greater than the default timeout of 120 seconds and less than 600 seconds}. Give long-running commands enough time to complete.
// * If the command does not complete within "timeout" seconds, the tool will return a status indicating that it is still running asynchronously. You can then use `read_bash` or `stop_bash`.
type bash = (_: {
// The bash command and arguments to run.
command: string,
// (Optional) Maximum time in seconds to wait for the command to complete when "async" is false. Default is 120 seconds if not provided.
timeout?: integer,
// Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time.
sessionId: string,
// If true, runs the command asynchronously. You can send input to the command using the `write_bash` tool and read its output using the `read_bash` tool.
async: boolean,
}) => any;


// Sends input to the specified command or bash session.
// * This tool can be used to send input to a running bash command or an interactive console app.
// * Bash commands are run in an interactive bash session with a TTY device and bash command processor.
// * sessionId (required) must match the sessionId used to invoke the async bash command.
// * You can send text, {up}, {down}, {left}, {right}, {enter}, and {backspace} as input.
// * Some applications present a list of options to select from. The selection is often denoted using ❯, >, or different formatting.
// * When presented with a list of items, you must make a selection by sending arrow keys like {up} or {down} to move the selection to the item that you want to select and then {enter} to select it.
// * The response will contain any output read after "delay" seconds. Delay should be appropriate for the task and never less than 10 seconds.
type write_bash = (_: {
// Indicates which bash session to run the command in. Multiple sessions may be used to run different commands at the same time.
sessionId: string,
// The input to send to the command or session.
input: string,
// (Optional) The amount of time in seconds to wait before reading the output that resulted from the input.
delay?: integer,
}) => any;


// Reads output from a bash command.
// * Reads the output of a command running in an "async" bash session.
// * The sessionId must be the same one used to invoke the bash command.
// * You can call this tool multiple times to read output produced since the last call.
// * Each request has a cost, so provide a reasonable "delay" parameter value for the task, to minimize the need for repeated reads that return no output.
// * If a read request generates no output, consider using exponential backoff in choosing the delay between reads of the same command.
// * Though `write_bash` accepts ANSI control codes, this tool does not include them in the output.
type read_bash = (_: {
// The ID of the shell session used to invoke the bash command.
sessionId: string,
// (Optional) The amount of time in seconds to wait before reading the output.
delay: integer,
}) => any;


// Stops a running bash command.
// * Stops a running bash command by terminating the entire bash session and process.
// * This tool can be used to stop commands that have not exited on their own.
// * Any environment variables defined will have to be redefined after using this tool if the same session ID is used to run a new command.
// * The sessionId must match the sessionId used to invoke the bash command.
type stop_bash = (_: {
// The ID of the bash session used to invoke the bash command.
sessionId: string,
}) => any;


// Editing tool for viewing, creating and editing files
// * State is persistent across command calls and discussions with the user
// * If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep
// * The `create` command cannot be used if the specified `path` already exists, or if parent directories don't exist
// * If a `command` generates a long output, output will be truncated and marked with `<file too long...`
// * The `undo_edit` command will undo the last edit made to the file at `path`
//
// Notes for using the `str_replace` command:
// * The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file
// * If the `old_str` parameter is not unique in the file, replacement will not be performed. Make sure to include enough context in `old_str` to make it unique
// * The `new_str` parameter should contain the edited lines that should replace the `old_str`
type str_replace_editor = (_: {
// The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.
command: "view" | "create" | "str_replace" | "insert" | "undo_edit",
// Required parameter of `create` command; the content of the file to be created.
file_text?: string,
// Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.
insert_line?: integer,
// Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert.
new_str?: string,
// Required parameter of `str_replace` command containing the string in `path` to replace. Leading and ending whitespaces from file content should be preserved!
old_str?: string,
// Absolute path to file or directory.
path: string,
// Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.
view_range?: array,
}) => any;


// Report progress on the task. Call when you complete a meaningful unit of work. Commits and pushes changes that are pending in the repo, then updates the PR description.
// * Use this tool at least once, and as early as possible once you've established a plan. Outline the complete plan as a checklist.
// * Use only when you have meaningful progress to report (you need to update the plan in the checklist, you have code changes to commit, or you have completed a new item in the checklist)
// * Use markdown checklists to show progress (- [x] for completed items, - [ ] for pending items).
// * Keep the checklist structure as consistent as you can between updates, while still being accurate and useful.
// * Don't use headers in the PR description, just the checklist.
// * If there are changes in the repo this tool will run `git add .`, `git commit -m <msg>`, and `git push`.
type report_progress = (_: {
// A short single line of text to use as the commit message
commitMessage: string,
// A description of work completed and remaining, using markdown checklists
prDescription: string,
}) => any;


} // namespace functions
"""

from qstar.common.tools import constraint_machine_spec
from qstar.common.tools.renderer_worker import get_end_of_tool_message_token, get_end_of_turn_token, get_disc_score_token

def get_render_constrained_extension(renderer_name: str):
    """
    Get the constrained extension for the renderer.
    """
    # This is a hack to get the renderer name for the constraint machine spec
    # The renderer name is not available in the current context
    # So we have to hardcode it here
    extension = constraint_machine_spec.constraint_machine_extension(
        harmony_renderer_name=renderer_name,
        encoding_name='orion_200k',
        final_token_sequences= frozenset(
            [
                (get_end_of_tool_message_token(renderer_name),),
                (get_end_of_turn_token(renderer_name), get_disc_score_token(renderer_name))
            ]),
        json_eot_tokens=frozenset([
            get_end_of_tool_message_token(renderer_name),
            get_end_of_turn_token(renderer_name),
        ]),
        tools=('functions.str_replace_editor', 'functions.bash', 'functions.report_progress'),
        channels=('analysis', 'final'),
        response_format_names=(),
        enable_json_constraining=False,
        none_channel_allowed=False,
    )

    return extension

def _match_pr_requirement(pr_str):
    # Extract title if it exists
    title_match = re.search(r'<pr_title>\s*(.*?)\s*</pr_title>', pr_str, re.DOTALL)
    # Extract description if it exists
    desc_match = re.search(r'<pr_description>\s*(.*?)\s*</pr_description>', pr_str, re.DOTALL)

    if title_match and desc_match:
        return True
    else:
        return False

def make_repo_script_list(repo, repo_directory, base_commit):
    """
    Create a list of bash commands to set up the repository for testing.
    This is the setup script for the instance image.
    """
    setup_commands = [
        [f"git clone -o origin https://github.com/{repo} {repo_directory}", "/"],
        [f"chmod -R 777 {repo_directory}", "/"], # So nonroot user can run tests
        [f"cd {repo_directory}", "/"],
        [f"git reset --hard {base_commit}", repo_directory],
        # Remove the remote so the agent won't see newer commits.
        ["git remote remove origin", repo_directory],
    ]

    return setup_commands

def to_sandbox_runtime(value: str) -> SandboxRuntime:
    return {
        "unsafe": SandboxRuntime.UNSAFE,
        "kata": SandboxRuntime.KATA_V2,
        "gvisor": SandboxRuntime.GVISOR,
    }[value.lower().strip()]

PROTECTED_FILES = [
    ".config.js",
    ".config.ts",
    "package.json",
    "pom.xml",
    "build.gradle",
    "cargo.toml",
    "go.mod",
    "go.sum",
    "setup.cfg",
    "pyproject.toml",
    "conftest.py",
    "pytest.ini",
    "requirements.txt",
    "requirements-dev.txt",
    "tox.ini",
    "setup.py",
    ".pre-commit-config.yaml",
    ".eslintrc",
    ".eslintrc.json",
    ".eslintrc.js",
    ".prettierrc",
    ".prettierrc.json",
    ".prettierrc.js",
    "webpack.config.js",
    "webpack.config.ts",
    "rollup.config.js",
    "rollup.config.ts",
    "vite.config.js",
    "vite.config.ts",
    ".mocharc.js",
    ".mocharc.json",
    ".mocharc.yaml",
    ".mocharc.yml",
    "jest.config.js",
    "settings.gradle",
    "CMakeLists.txt",
    "Gemfile",
    "Gemfile.lock",
    "rust-toolchain",
    "Dockerfile",
    ".dockerignore",
    ".gitignore",
    ".gitattributes",
    "Makefile",
    ".env",
]


async def get_model_patch(
    terminal_session: TerminalSession,
    base_commit: str,
    repo_root: str,
    protected_files: list[str],
) -> ModelPatch:
    # Download the model diff (excluding config files to avoid hacks)
    exclude_patterns = shlex.join([f":(exclude){file}" for file in protected_files])
    model_patch_raw = await run_with_retries(
        terminal_session,
        f"""
cd {repo_root}
# Do not include warnings about CRLF line endings in git diff output.
git config core.safecrlf false
git add -N .
git diff --binary {base_commit} -- . {exclude_patterns}
""",
        attempts=3,
    )

    a = ModelPatch(model_patch_raw.decode())

    return ModelPatch(model_patch_raw.decode()), model_patch_raw.decode()

#model_patch.test_patch
async def _upload_and_apply(
    session: TerminalSession,
    patch: str,
    workdir: str,
) -> None:
    tmpfile = "/tmp/" + uuid4().hex
    if not patch.strip():
        p = NOOP_PATCH
    else:
        p = patch
    print("gohere111")
    print(p)
    print("gohere222")
    await session.session.run(UploadFile(path=tmpfile, data=p.encode()))
    r = await session.session.run(Exec(["git", "apply", "-v", tmpfile], workdir=workdir, timeout=60))
    print(r)

NODEJS_VERSION='22.14.0'
TMP_DRI="/usr/local/nodejsinstall"
TIMEOUT=1200

NOOP_PATCH = """\
diff --git a/this_is_invisible.py b/this_is_invisible.py
new file mode 100644
index 0000000..e69de29
--- /dev/null
+++ b/this_is_invisible.py
@@ -0,0 +1 @@
+# This is a commented out line
"""

async def main(instance, juice=1024, max_episode_steps=50, give_hint=False):

    instance_id = instance['instance_id']
    hints_text = instance['hints_text']
    issue = instance['problem_statement']
    repo_name = instance["repo"]

    test_spec = make_test_spec(instance)

    current_time = datetime.now().strftime("%Y%m%d-%H-%M-%S")
    gpt_log_folder = os.path.join("./gpt-log", instance_id + f'-{current_time}')
    output_dir = os.path.join('./outputs', instance_id)
    os.makedirs(output_dir, exist_ok=True)

    # Build CaaS Container for the current instance
    # We have saved all 500 instance images for SWE-Bench_Verified

    retries = 50
    for attempt in range(retries):
        try:
            CAAS_ENDPOINT = "https://southcentralus.caas.azure.com" # "https://eastus2.caas.azure.com"
            CAAS_IMAGE1 = f'acrbuiltincaasglobalame.azurecr.io/caas-swe-bench:20250115a-{instance_id}'
            if USE_SWE_WORKER:
                CAAS_IMAGE = f'acrbuiltincaasglobalame.azurecr.io/caas-swe-bench:20250115a-{instance_id}'
            else:
                #CAAS_IMAGE = "actions-runner-terminal-server"
                CAAS_IMAGE = "actions-runner-terminal-server-padawan"
            blob_name = f"node-v{NODEJS_VERSION}-linux-x64.tar.gz"
            mnt = [VolumeMount(host=f"/mnt/azure_blob/tools/{blob_name}", container=f"{TMP_DRI}/{blob_name}")]

            caas = caas_api(endpoint=CAAS_ENDPOINT)

            caas_session = await caas.new_session(
                image=CAAS_IMAGE, num_gpus=None, sandbox_runtime=to_sandbox_runtime("unsafe"), idle_ttl=1200, memory_limit="50g", cpu_limit="16", network=NetworkMode.CAAS_PUBLIC_ONLY, volume_mounts=mnt
            )
            terminal_session = TerminalSession(caas_session)

            if not USE_SWE_WORKER:
                commands = make_repo_script_list(repo_name, "/testbed", instance["base_commit"])
                for command, workdir in commands:
                    r = await terminal_session.session.run(RawExec(shlex.split(command), timeout=120, workdir=workdir))
                    print(r)

            # Enforce the workdir to be `/testbed`
            bashrc_command = f"echo 'cd /testbed' >> /root/.bashrc"
            await terminal_session.session.run(BashScript(bashrc_command, timeout=1200))

            if USE_SWE_WORKER:
                # Install Node.js
                await terminal_session.session.run(RawExec(["bash", "-c", f"set -x && time tar -xf {blob_name} -C /usr && node -v && npm -v"], workdir=TMP_DRI))

                # Upload Padawan tools: You must use the padawan-v2 tool here, See README for more details
                PADAWAN_TOOLS_DIR = '/root/code/glass/msft_tools/padawan_v2/padawan_tools'
                await terminal_session.session.run(Exec(['bash', '-lc', 'rm -rf /usr/local/bin/oai'], timeout=600, workdir='/', env=None))
                await terminal_session.session.run(Exec(['bash', '-lc', 'mkdir /usr/local/bin/padawan_tools'], timeout=600, workdir='/', env=None))
                with open(os.path.join(PADAWAN_TOOLS_DIR, 'index.js'), "rb") as binary_file:
                    await terminal_session.session.run(UploadFile('/usr/local/bin/padawan_tools/index.js', binary_file.read()))
                with open(os.path.join(PADAWAN_TOOLS_DIR, 'tool_wrapper.js'), "rb") as binary_file:
                    await terminal_session.session.run(UploadFile('/usr/local/bin/padawan_tools/tool_wrapper.js', binary_file.read()))

            # Make the tools executable
            repo_root = '/testbed'
            # output = await terminal_session.session.run(Exec(['git', 'branch', '--show-current'], timeout=TIMEOUT, workdir=repo_root, env=None))
            mock_config = {'location': repo_root, 'branchName': 'main'} # location needs to be real in padawan_tools; but branchName can be anything
            # print(mock_config)
            mock_config_json = json.dumps(mock_config)

            cmd_str_replace = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js str_replace_editor '{mock_config_json}' "$@"'''
            await terminal_session.session.run(UploadFile('/usr/local/bin/str_replace_editor', cmd_str_replace.encode('utf-8')))
            await terminal_session.session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/str_replace_editor'], timeout=600, workdir='/', env=None))

            cmd_report_progress = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js report_progress '{mock_config_json}' "$@"'''
            await terminal_session.session.run(UploadFile('/usr/local/bin/report_progress', cmd_report_progress.encode('utf-8')))
            await terminal_session.session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/report_progress'], timeout=600, workdir='/', env=None))

            cmd_bash = f'''node /usr/local/bin/padawan_tools/tool_wrapper.js bash '{mock_config_json}' "$@"'''
            await terminal_session.session.run(UploadFile('/usr/local/bin/pdw_bash', cmd_bash.encode('utf-8')))
            await terminal_session.session.run(Exec(['bash', '-lc', 'chmod +x /usr/local/bin/pdw_bash'], timeout=TIMEOUT, workdir='/', env=None))

            # Set up git user for report_progress
            # await terminal_session.session.run(Exec(['git', 'config', '--global', 'user.name', 'Padawan'], timeout=TIMEOUT, workdir=repo_root, env=None))
            # await terminal_session.session.run(Exec(['git', 'config', '--global', 'user.email', '<EMAIL>'], timeout=TIMEOUT, workdir=repo_root, env=None))

            container = CaasContainer(
                caas_endpoint=CAAS_ENDPOINT,
                image_name=CAAS_IMAGE,
                caas_session_state=caas_session.save(),
            )

            # Get CaaS supported tool_kit
            toolkit = DefaultMultiToolKit(
                tools=[
                    DeepSWECaasPadawanTool(
                        container=container,
                        terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
                    ),
            ])

            tools_section = {"functions": padawan_tool_instruction}

            # Set up Agent
            engine_url = "http://localhost:5001/v1"
            # Replace with your proper renderer
            renderer_name = "harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc"
            #renderer_name = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"
            # renderer_name = "harmony_v4.0.15_berry_v3_1mil_orion_lpe_short"
            renderer = get_renderer(renderer_name)
            extension = get_render_constrained_extension(renderer_name)

            user_prompt = get_user_message(repo_name, instance_id, issue, workdir='/testbed')

            convo = chat.Conversation(
                messages=[
                    chat.Message.system(
                        model_identity_desc=PADAWAN_SYSTEM_PROMPT,
                        tools_section=tools_section,
                        channel_config=chat.SystemChannelConfig(valid_channels=("analysis","final"), channel_required=True),
                        metadata=chat.SystemContentMetadata(reward_multiplier=juice)
                    ),
                    chat.Message.user(user_prompt),
            ]
            )
            # Increase the yield budget for the assistant
            convo.metadata.header_yields_budget_total_override=256
            convo.metadata.header_yields_budget_for_action=256

            write_messages_to_file(convo.messages, f"episode_000.jsonl", folder=gpt_log_folder)
            with open(os.path.join(output_dir, 'user_prompt.txt'), 'w') as file:
                convo_tokens = renderer.render_for_completion(convo, Role.ASSISTANT)
                file.write(renderer.encoding.decode(convo_tokens))

            if USE_BUS:
                bus_tc_config = BusTokenCompleter.Config(
                    topic_mode_or_user="evaluation",
                    topic_or_snapshot="az://orngscuscresco/twapi/mini/e/chenliang1-mix15-pdw2-ev3-itc8k-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run2-continue2-setup-run5/policy/step_000020/",
                )

                # Constructing from configs is optional, but provides some ergonomics benefits.
                message_completer_config = TokenMessageCompleter.Config(
                    token_completer_config=bus_tc_config,
                    completion_params={"temperature": 1, "extensions": [extension]},
                    renderer=renderer,
                )
            else:
                message_completer_config = TokenMessageCompleter.Config(
                    token_completer_config=LegacyRestTokenCompleter.Config(
                        api_base=engine_url,
                        backend=CompleterBackend.TEXT_BACKEND,  # use TEXT_BACKEND for research (non-api.openai.com) engines
                    ),
                    # completion_params={"temperature": 1},
                    completion_params={"temperature": 1,'extensions': [extension]},
                    renderer=renderer,
                )
            message_completer = message_completer_config.build()

            valid_nontool_recipients = {
                m.author.display_name()
                for m in convo.messages
                if m.author.role != Role.TOOL
            }
            valid_recipients = (
                valid_nontool_recipients
                | (set([tool.name for tool in toolkit.tools]) if toolkit else set())
                | {"all"}
                | {"functions.report_progress"}
                | {"functions.bash"}
                | {"functions.str_replace_editor"}
                | {"functions.write_bash"}
                | {"functions.read_bash"}
                | {"functions.stop_bash"}
            )
            # print(f"valid_recipients: {valid_recipients}")

            # Solve the Problem
            num_report_progress = 0
            #max_steps = 0
            for elapsed_step in range(max_episode_steps):
                #max_steps += 1
                is_last_step = elapsed_step == max_episode_steps - 1
                for completion_attempt in range(retries): # for NV4 little instability in inference.
                    try:
                        completion = await message_completer.async_completion(
                            conversations=[convo], n=1, seed=0, end_header=is_last_step
                        )
                        choice = completion.choices[0]
                        messages = choice.get_messages(
                            parse_error_mode=ParseErrorMode.SYSTEM_ERROR
                        )
                        break
                    except Exception as e:
                        if completion_attempt < retries-1 :
                            print(f"Error in completion: {e}")
                            time.sleep(1)
                            continue
                        else:
                            raise ValueError(f"Error in completion: {e}")

                messages_write = messages.copy()
                convo = convo.with_suffix(*messages)

                if messages[-1].recipient == 'functions.report_progress':
                    num_report_progress += 1
                #if max_steps > 10:
                #    break
                if (
                    is_last_step
                    or messages[-1].end_turn
                    or (messages[-1].recipient not in valid_recipients and messages[-1].recipient.split(".")[0] not in valid_recipients)
                ):
                    write_messages_to_file(messages_write, f"episode_{elapsed_step+1:03}.jsonl", folder=gpt_log_folder)
                    break

                # call tool once
                print("gohere")
                print(messages[-1])
                async for tool_message in take_one_step_with_tools(
                    prefix_convo=convo.prefix(-1),
                    message=messages[-1],
                    toolkit=toolkit,
                ):
                    if convo.messages[-1].id == tool_message.id:
                        convo.messages[-1] = tool_message
                    else:
                        convo = convo.with_suffix(tool_message)

                    messages_write.append(tool_message)

                write_messages_to_file(messages_write, f"episode_{elapsed_step+1:03}.jsonl", folder=gpt_log_folder)

            print(f"{instance_id}: sampling for convo completed, total episode: {elapsed_step}")

            # Get PR Description
            pr_desc_prompt = get_prDescriptionUserMessage()
            convo = convo.with_suffix(chat.Message.user(pr_desc_prompt))

            for completion_attempt in range(retries): # for NV4 little instability in inference.
                try:
                    completion = await message_completer.async_completion(
                        conversations=[convo], n=1, seed=0, end_header=False
                    )
                    choice = completion.choices[0]
                    messages = choice.get_messages(
                        parse_error_mode=ParseErrorMode.SYSTEM_ERROR
                    )
                    break
                except Exception as e:
                    if completion_attempt < retries-1 :
                        print(f"Error in completion: {e}")
                        time.sleep(1)
                        continue
                    else:
                        raise ValueError(f"Error in completion: {e}")

            messages_write = messages.copy()
            convo = convo.with_suffix(*messages)
            write_messages_to_file(messages_write, f"pr_description.jsonl", folder=gpt_log_folder)
            print(f"{instance_id}: pr description for convo completed")
            pr_check = _match_pr_requirement(str(messages[-1].content))

            write_messages_to_file(convo.messages, f"all_episodes.jsonl", folder=gpt_log_folder)

            # Commit everything before eval
            # await terminal_session.session.run(Exec(['git', 'add', '.'], timeout=TIMEOUT, workdir=repo_root, env=None))
            # commit_output = await terminal_session.session.run(RawExec(['git', 'commit', '-a', '-m', 'Automated commit by Padawan'], timeout=TIMEOUT, workdir=repo_root, env=None))
            # print(f"Exit Code: {commit_output[0]} Output: {commit_output[1].decode()}")

            # Evaluation
            print(f"{instance_id}: evalution started")
            with open(os.path.join(output_dir, 'eval.sh'), 'w') as file:
                for text in test_spec.eval_script_list:
                    file.write(text + "\n")

            if USE_SWE_GRADER:
                container1 = await CaasContainer.new(caas_endpoint=CAAS_ENDPOINT,
                                                image_name=CAAS_IMAGE1,
                                                idle_ttl=1200,
                                                memory_limit="16g",
                                                cpu_limit="16",
                                                network=NetworkMode.CAAS_PUBLIC_ONLY,
                                                volume_mounts=mnt)

            if USE_SWE_GRADER:
                model_patch, model_patch_raw = await get_model_patch(terminal_session, instance["base_commit"], "/testbed", PROTECTED_FILES)
                #await _upload_and_apply(container1.terminal_session, model_patch.test_patch, "/testbed")
                await _upload_and_apply(container1.terminal_session, model_patch_raw, "/testbed")

                with open(os.path.join(output_dir, 'eval.sh'), "rb") as binary_file:
                    await container1.terminal_session.session.run(UploadFile('/eval.sh', binary_file.read()))
                r = await container1.terminal_session.session.run(RawExec(['bash', '/eval.sh'], timeout=600, workdir='/', env=None))
            else:
                with open(os.path.join(output_dir, 'eval.sh'), "rb") as binary_file:
                    await terminal_session.session.run(UploadFile('/eval.sh', binary_file.read()))
                r = await terminal_session.session.run(RawExec(['bash', '/eval.sh'], timeout=600, workdir='/', env=None))

            test_output = r[1].decode("utf-8").strip()

            test_output_path = os.path.join(output_dir, f'test_output.txt')
            with open(test_output_path, "w") as f:
                f.write(test_output)

            report = get_eval_report_hack(
                test_spec=test_spec,
                log_path=test_output_path,
                include_tests_status=True,
            )
            report[instance_id]['pr_check'] = int(pr_check)
            report[instance_id]['num_report_progress'] = num_report_progress
            print(f"Result for {instance_id}: resolved: {report[instance_id]['resolved']} | pr_check: {report[instance_id]['pr_check']} | num_report_progress: {report[instance_id]['num_report_progress']}")

            report_path = os.path.join(output_dir, 'report.json')
            with open(report_path, "w") as f:
                f.write(json.dumps(report, indent=4))

            if USE_SWE_GRADER:
                if container1:
                    await container1.teardown()

            return int(report[instance_id]['resolved'])

        except Exception as e:
            print(f"instance {instance_id} attempt {attempt + 1} failed with error {e}, retry ...")
            if attempt < retries -1 :
                time.sleep(4)
            else:
                raise ValueError(f"instance {instance_id} failed!!!")

async def process_instance(instance, juice=1024, max_episode_steps=100, give_hint=False):
    return await main(instance=instance, juice=juice, max_episode_steps=max_episode_steps, give_hint=give_hint)

async def process_all(dataset, juice=1024, max_episode_steps=100, give_hint=False, concurrency_limit=10):
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []

    async def process_with_semaphore(instance):
        async with semaphore:
            return await process_instance(instance, juice=juice, max_episode_steps=max_episode_steps, give_hint=give_hint)

    tasks = [process_with_semaphore(instance) for instance in dataset]
    for task in asyncio.as_completed(tasks):
        results.append(await task)  # Collect results as they complete
    return results

def get_checked_instances(outputs_folder):
    checked_instances = set()
    # Iterate through subfolders
    for subfolder in os.listdir(outputs_folder):
        subfolder_path = os.path.join(outputs_folder, subfolder)
        if os.path.isdir(subfolder_path):  # Ensure it's a directory
            report_file = os.path.join(subfolder_path, "report.json")
            if os.path.exists(report_file):
                checked_instances.add(subfolder)
    return checked_instances


def load_from_jsonl(jsonl_path):
    """
    Load dataset from a jsonl file.

    Args:
        jsonl_path (str): Path to the jsonl file

    Returns:
        datasets.Dataset: A Hugging Face dataset object
    """
    try:
        dataset = load_dataset("json", data_files=jsonl_path)
        # The dataset is usually wrapped in a split like 'train'
        # Get the first available split
        first_split = list(dataset.keys())[0]
        return dataset[first_split]
    except Exception as e:
        print(f"Error loading jsonl file {jsonl_path}: {e}")
        return None

if __name__ == "__main__":

    dataset_name_or_path = '/root/code/glass/data/jsonl/swe-bench-verified-vsc.jsonl'
    # Check if the dataset file exists, if not download it from Azure blob
    if not os.path.exists(dataset_name_or_path):
        print(f"Dataset file {dataset_name_or_path} not found. Downloading from Azure blob...")
        os.makedirs(os.path.dirname(dataset_name_or_path), exist_ok=True)
        import subprocess
        download_cmd = f'bbb cp az://orngscuscresco/data/luw/swe-bench-eval-vsc/swe_bench_train_updated.jsonl {dataset_name_or_path}'
        result = subprocess.run(download_cmd, shell=True)
        if result.returncode != 0:
            raise RuntimeError(f"Failed to download dataset file: {download_cmd}")
        print(f"Dataset downloaded successfully to {dataset_name_or_path}")

    # Check if the file is a jsonl file
    if dataset_name_or_path.endswith('.jsonl'):
        dataset = load_from_jsonl(dataset_name_or_path)
    else:
        dataset = load_from_disk(dataset_name_or_path)
        split = 'test'
        if split in dataset:
            dataset = dataset[split]

    lens = np.array(list(map(len, dataset["problem_statement"])))
    dataset = dataset.select(np.argsort(lens))

    os.makedirs('./outputs', exist_ok=True)
    existing_ids = get_checked_instances('./outputs')
    if len(existing_ids) > 0:
        dataset = dataset.filter(
            lambda x: x["instance_id"] not in existing_ids,
            desc="Filtering out existing ids",
            load_from_cache_file=False,
        )

    dataset = dataset.filter(
        lambda x: x["instance_id"] not in msft_excludes,
        desc="Filtering out MSFT exclude instances",
        load_from_cache_file=False,
    )
    dataset = dataset.filter(
        lambda x: "astropy__astropy" in x["instance_id"] ,
        #lambda x: "sympy__sympy" in x["instance_id"],
        #lambda x: "sympy__sympy-11618" in x["instance_id"],
        #lambda x: "astropy__astropy-13453" in x["instance_id"], #"astropy__astropy-14096" in x["instance_id"] or  "astropy__astropy-13453" 14995
        desc="astropy",
        load_from_cache_file=False,
    )
    print(f'Run on {len(dataset)} instances.')

    tool_name_or_path = '/root/code/glass/msft_tools/padawan_v2/padawan_tools'
    # Check if the tool file exists, if not download it from Azure blob
    if not os.path.exists(tool_name_or_path):
        print(f"Padawan-v2 tool file {tool_name_or_path} not found. Downloading from Azure blob...")
        os.makedirs(os.path.dirname(tool_name_or_path), exist_ok=True)
        import subprocess
        download_cmd = f'bbb cptree az://orngscuscresco/data/jadhuang/tools/padawan_tools/20250721 /root/code/glass/msft_tools/padawan_v2/padawan_tools'
        result = subprocess.run(download_cmd, shell=True)
        if result.returncode != 0:
            raise RuntimeError(f"Failed to download tool file: {download_cmd}")
        print(f"Padawan-v2 tool downloaded successfully to {tool_name_or_path}")

    juice = 768
    max_episode_steps = 256
    give_hint = False
    parallel = 100
    passes = asyncio.run(process_all(dataset, juice=juice, max_episode_steps=max_episode_steps, give_hint=give_hint, concurrency_limit=parallel))

    print("*"*100)
    print(f'Evaluation Results: ')
    print(passes)
    print(f"mean = {sum(passes)/len(passes)} | len = {len(passes)}")
