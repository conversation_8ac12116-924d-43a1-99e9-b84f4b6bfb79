import asyncio
import importlib
import logging
import os
from dataclasses import KW_ONLY, dataclass, field
from typing import Optional, Type

import msgpack
from caas.api import CaasSession, caas_api
from caas.commands import DownloadFileFromContainer, RawExec, UploadFile
from caas.internal.errors import Container<PERSON>illedError, ExecError, TimedOutError
from caas.protocol import VolumeMount
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import <PERSON><PERSON>sC<PERSON>r
from caas_tool.caas_container_tool import CaasContainerTool
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CAAS_IMAGE
from prbot_msft.caas_handle_utils import _retry_callback
from prbot_msft.swebench_hard.container import Handle
from pydantic import ConfigDict, TypeAdapter, dataclasses
from tenacity import retry, stop_after_attempt, wait_fixed, wait_random_exponential

logger = logging.getLogger(__name__)


@dataclasses.dataclass(config=ConfigDict(arbitrary_types_allowed=True))
class TerminalSessionHandle(Handle):
    _: KW_ONLY
    caas_endpoint: str = CAAS_ENDPOINT
    image_name: str = CAAS_IMAGE
    idle_ttl: int = 1_200  # seconds
    cpu_limit: str = "16.0"
    memory_limit: str = "64g"
    git_volume_mounts: list[VolumeMount] = field(default_factory=list)
    _ts_handle: TerminalSession = field(default=None, init=False, repr=True)

    @property
    def id(self):
        return self._ts_handle.session.session_id

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.graceful_teardown(self._ts_handle)

    async def graceful_teardown(self, ts_handle):
        try:
            await ts_handle.close()
        except Exception as e:
            logger.exception(f"(ignored) Failed to teardown _ts_handle: {e!r}")

    def dump(self) -> bytes:
        raise NotImplementedError()

    @classmethod
    async def load(cls, data: bytes) -> "TerminalSessionHandle":
        raise NotImplementedError()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_random_exponential(multiplier=1, max=64),
        retry_error_callback=_retry_callback,
    )
    async def commit(self, name, tag):
        # Cannot exceed lifetime of container
        async with asyncio.timeout(30 * 60):
            image_name = await self._ts_handle.session.commit(
                name=name,
                tag=tag,
                push=True,
            )
            verify_session = await CaasContainer.new(
                caas_endpoint=self.caas_endpoint,
                image_name=image_name,
                idle_ttl=self.idle_ttl,
                cpu_limit=self.cpu_limit,
                memory_limit=self.memory_limit,
            )
            await self.graceful_teardown(verify_session)
            return image_name

    @property
    def raw_handle(self) -> TerminalSession:
        return self._ts_handle

    @retry(
        stop=stop_after_attempt(10),
        wait=wait_random_exponential(multiplier=1, max=64),
        retry_error_callback=_retry_callback,
    )
    async def __async_post_init__(self):
        self._ts_handle = await TerminalSession.new_session(
            caas_api(endpoint=self.caas_endpoint),
            image=self.image_name,
            idle_ttl=self.idle_ttl,
            cpu_limit=self.cpu_limit,
            memory_limit=self.memory_limit,
            volume_mounts=self.git_volume_mounts,
        )

    @classmethod
    async def create(cls, **kwargs):
        if "image_name" in kwargs and kwargs["image_name"] is None:
            kwargs.pop("image_name")
        instance = cls(**kwargs)
        await instance.__async_post_init__()
        return instance

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry_error_callback=_retry_callback)
    async def exec(
        self,
        cmd: list[str],
        *,
        timeout: int,
        workdir: str,
        env: Optional[dict] = None,
        return_bytes: bool = True,
        user: Optional[str] = None,
    ) -> tuple[int, str | bytes]:
        timeout = timeout // 1000  # Convert milliseconds to seconds
        try:
            user = "" if user is None else user
            env = {} if env is None else env
            with open("/var/log/supervisor/caas_exec.log", "a") as log_file:
                log_file.write(
                    f"Executing command: {cmd} in {workdir} with env: {env}, timeout: {timeout}, and user: {user}\n"
                )
            exit_code, stdout_bytes = await self._ts_handle.session.run(
                RawExec(cmd=cmd, workdir=workdir, timeout=timeout, env=env, user=user)
            )
        except ExecError as e:
            if isinstance(e, (TimedOutError, ContainerKilledError)):
                raise
            else:
                if len(e.output) > 0:
                    raise
                else:
                    exit_code, stdout_bytes = e.status, e.output
        except Exception as e:
            exit_code, stdout_bytes = 1, repr(e).encode()
        if return_bytes:
            return exit_code, stdout_bytes
        else:
            return exit_code, stdout_bytes.decode("utf-8", errors="replace")

    async def upload_file(self, remote_filename: str, contents: bytes):
        await self._ts_handle.session.run(UploadFile(remote_filename, contents))

    async def download_file(self, remote_filename: str):
        return await self._ts_handle.session.run(DownloadFileFromContainer(remote_filename))

    def __getattr__(self, name):
        return getattr(self._caas_handle, name)


if __name__ == "__main__":
    import asyncio

    async def mega():
        import json

        handle = await TerminalSessionHandle.create()
        rv, out = await handle.exec(
            ["echo", "-n", "hello world"], timeout=30, workdir="/", return_bytes=False
        )
        assert rv == 0
        assert out == "hello world", json.dumps(out)

        await handle.upload_file(
            remote_filename="/hello", contents="echo -n 'hello again'".encode()
        )
        rv, out = await handle.exec(["bash", "/hello"], timeout=30, workdir="/")
        assert rv == 0
        assert out.decode("utf-8") == "hello again", json.dumps(out)

    asyncio.run(mega())
