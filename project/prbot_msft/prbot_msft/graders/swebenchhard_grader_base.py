import asyncio
import dataclasses
import inspect
import traceback
from datetime import datetime
from typing import Sequence, final

import aiofiles
import berry
import chz
import diffs.v4.chat as diffs_v4
import structlog
from berry.sample import GraderError
from caas import TimedOutError
from caas import api as caas_api
from caas.protocol import VolumeMount
from caas_tool.caas_container import CaasContainer
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CAAS_IMAGE
from prbot_msft.caas_handle_utils import C<PERSON>sHandle
from prbot_msft.graders.exceptions import (
    ContainerToolStateMissingException,
    FreshCaasException,
    FreshExampleException,
    ReuseCaasException,
)
from prbot_msft.swebench_hard.container import success_output
from prbot_msft.swebench_hard.example import Example
from prbot_msft.swebench_hard.stream import SweDatum
from qstar.common import datapoint, types
from qstar.graders import grader as grader_module
from qstar.graders.repo_graders_v3.delegation_utils import collapse_convo
from qstar.graders.repo_graders_v3.rfs_grader_base import close_loop, get_loop
from qstar.sample_completers import sample_completer

logger = structlog.stdlib.get_logger(component=__name__)


async def get_excluded_files(example):
    excluded_patterns = ["run_tests.sh", "junit_xml_reports/"]
    timeout = 60_000
    filenames = []
    for pattern in excluded_patterns:
        git_ls_files_tracked_output = await success_output(
            handle=example.container_handle,
            cmd=["git", "ls-files", "--exclude-standard", "-z", "--", pattern],
            workdir=example.repo_directory,
            timeout=timeout,
            return_bytes=False,
        )
        filenames.extend(git_ls_files_tracked_output.split("\0"))
        git_ls_files_untracked_output = await success_output(
            handle=example.container_handle,
            cmd=["git", "ls-files", "--exclude-standard", "--others", "-z", "--", pattern],
            workdir=example.repo_directory,
            timeout=timeout,
            return_bytes=False,
        )
        filenames.extend(git_ls_files_untracked_output.split("\0"))
    return set(filenames)


async def update_example_with_pre_untracked(example: Example, pre_untracked: set[str]) -> None:
    filenames_to_ignore = await get_excluded_files(example)
    example._pre_untracked = pre_untracked  # overwrite pre_untracked
    await log_to_file(
        f"Filenames to ignore: {filenames_to_ignore}", instance_id=example.swe_datum.instance_id
    )
    example._pre_untracked.update(filenames_to_ignore)
    await log_to_file(
        f"Updated pre_untracked: {example.pre_untracked}", instance_id=example.swe_datum.instance_id
    )


async def get_fresh_pre_untracked(fresh_handle: CaasHandle, instance: SweDatum) -> set[str]:
    async with fresh_handle:
        # grab pre-untracked from a fresh caas handle
        version = instance.version if instance.version else "sbh-12878"
        original_example = await Example.create(
            swe_datum=instance,
            container_handle=fresh_handle,
            timeout=900_000,
            run_setup=version.endswith("-noexec"),
            run_tests=False,
        )
        await log_to_file(
            f"got pre untracked: {original_example.pre_untracked}", instance_id=instance.instance_id
        )
    return original_example.pre_untracked


@chz.chz(typecheck=True)
class SWEBenchHardGraderBase(grader_module.Grader[datapoint.HarmonyCompletionDatapoint]):
    caas_blobstore_container_name: str = chz.field(default="oaistrawberryacecus")
    caas_endpoint: str = chz.field(default=CAAS_ENDPOINT)
    caas_container_image: str = chz.field(default=CAAS_IMAGE)
    reward_name: str = "swebenchhard_grader"

    def get_caas_client(self) -> caas_api.Caas:
        return asyncio.run(caas_api.caas_api(self.caas_endpoint))

    @property
    def loop(self) -> asyncio.AbstractEventLoop:
        return get_loop()

    @classmethod
    def close_loop(cls) -> None:
        return close_loop()

    @final
    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]]:
        # adding this assert to fail loudly when people use non-harmony
        for sample in samples:
            assert isinstance(sample.gt_datapoint, datapoint.HarmonyCompletionDatapoint)

        futures = []
        for sample in samples:
            unique_id = sample.gt_datapoint.unique_id
            assert unique_id is not None
            assert sample.conversation is not None
            async_fut = self._evaluate_code(sample)
            future = asyncio.run_coroutine_threadsafe(async_fut, self.loop)
            futures.append(future)

        samples_with_grade = []
        for sample, future in zip(samples, futures):
            try:
                graded_sample = future.result()
            except Exception as e:
                graded_sample = make_error_sample(
                    sample, e, grader_name=type(self).__name__, reward_name=self.reward_name
                )
            samples_with_grade.append(graded_sample)

        self._regrade_batch_inner(samples_with_grade)

        return samples_with_grade

    def _regrade_batch_inner(
        self,
        samples: list[types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]],
    ) -> None:
        pass

    async def _evaluate_code(
        self,
        sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    ) -> types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]:
        return NotImplementedError()


def make_error_sample(
    sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    exc: Exception,
    grader_name: str,
    reward_name: str,
):
    instance = get_instance_from_metadata(sample.gt_datapoint.metadata)
    grader_error = GraderError.from_exception(exc)
    log_to_file_sync(
        f"ERROR: {grader_name}: {type(exc).__name__}: {exc}\nTraceback:\n{traceback.format_exc()}",
        instance_id=instance.instance_id,
    )
    if isinstance(exc, FreshCaasException):
        return dataclasses.replace(
            sample,
            errors_blamed_on_system=sample.errors_blamed_on_system | {"fresh_caas_error"},
        ).with_grader_error(grader_error)
    elif isinstance(exc, ContainerToolStateMissingException):
        return sample.with_correctness(
            reward_name=reward_name,
            is_correct=False,
            additional_metadata={
                "grade_metadata": {
                    "error": "No container tool state, likely due to lack of a model call to the tool",
                },
                "original_metadata": {**sample.gt_datapoint.metadata},
            },
        )
    elif isinstance(exc, ReuseCaasException):
        return dataclasses.replace(
            sample,
            errors_blamed_on_system=sample.errors_blamed_on_system | {"reuse_caas_error"},
        ).with_grader_error(grader_error)
    elif isinstance(exc, FreshExampleException):
        return dataclasses.replace(
            sample,
            errors_blamed_on_system=sample.errors_blamed_on_system | {"fresh_example_error"},
        ).with_grader_error(grader_error)
    elif isinstance(exc, TimedOutError):
        return dataclasses.replace(
            sample,
            errors_blamed_on_system=sample.errors_blamed_on_system | {"timedout_error"},
        ).with_grader_error(grader_error)
    else:
        logger.warning(f"Flaky CaaS error in {grader_name}", exc_info=True)
        log_to_file_sync("Error unhandled, throwing other_error", instance_id=instance.instance_id)
        return dataclasses.replace(
            sample,
            errors_blamed_on_system=sample.errors_blamed_on_system | {"other_error"},
        ).with_grader_error(grader_error)


async def log_to_file(
    message: str,
    instance_id: str,
    log_file: str = "/var/log/supervisor/swebenchhard_grader_results.log",
):
    """Logs a message to the specified log file with a timestamp."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    async with aiofiles.open(log_file, "a") as f:
        await f.write(f"[{timestamp}] [{instance_id}] {message}\n")


def log_to_file_sync(
    message: str,
    instance_id: str,
    log_file: str = "/var/log/supervisor/swebenchhard_grader_results.log",
):
    """Logs a message to the specified log file with a timestamp."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_file, "a") as f:
        f.write(f"[{timestamp}] [{instance_id}] {message}\n")


async def setup_container(
    container_tool_state: bytes, git_volume_mounts: list[VolumeMount] = []
) -> CaasHandle:
    """Sets up the container and returns a CaasHandle."""
    caas_container = await CaasContainer.load(container_tool_state)
    caas_handle = CaasHandle(git_volume_mounts=git_volume_mounts)
    caas_handle._caas_handle = caas_container
    return caas_handle


def handle_error(sample, e, reward_name):
    """Handles errors by logging and returning a GraderError."""
    logger.exception("Unexpected error in grader", e=e)
    grader_error = GraderError.from_exception(e)
    return sample.with_grader_error(grader_error, with_reward=reward_name)


def get_instance_from_metadata(metadata: dict) -> SweDatum:
    valid_params = set(inspect.signature(SweDatum).parameters.keys())
    filtered_metadata = {k: v for k, v in metadata.items() if k in valid_params}
    instance = SweDatum(**filtered_metadata)
    return instance


def maybe_get_caas_state(sample: berry.SampleWithCompletion) -> bytes | None:
    # copied from project/caas_tasks
    CAAS_KEYS = ("caas_container", "caas_container_network_enabled_after_setup")
    tool_resources = sample.ephemeral_metadata.get("resources", {})
    for key in CAAS_KEYS:
        maybe_state_bytes = tool_resources.get(key)
        if maybe_state_bytes is not None:
            assert isinstance(maybe_state_bytes, bytes)
            return maybe_state_bytes
    return None


def extract_patch(sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint]) -> str:
    patches = []
    convo = collapse_convo(sample)
    for message in convo.messages:
        text = diffs_v4.get_patch_code(message)
        if not text:
            continue

        patch_text = diffs_v4.extract_patch_text(text)
        if not patch_text:
            continue

        # strip begin/end patch markers
        patch_text = "\n".join(patch_text.splitlines()[1:-1])
        patches.append(patch_text)

    return "\n\n".join(patches)
