from __future__ import annotations

import asyncio
import dataclasses
import json
import logging
import traceback
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Optional, Sequence, final

import chz
import structlog
import tenacity
from berry.sample import GraderError
from caas.protocol import VolumeMount
from prbot_msft.caas_handle_utils import <PERSON><PERSON>s<PERSON><PERSON><PERSON>
from prbot_msft.graders.exceptions import (
    ContainerToolStateMissingException,
    FreshCaasException,
    FreshExampleException,
    ReuseCaasException,
)
from prbot_msft.graders.swebenchhard_criteria_prompts import patch_prompt
from prbot_msft.graders.swebenchhard_grader_base import (
    get_fresh_pre_untracked,
    get_instance_from_metadata,
    log_to_file,
    log_to_file_sync,
    make_error_sample,
    maybe_get_caas_state,
    setup_container,
    update_example_with_pre_untracked,
)
from prbot_msft.graders.swebenchhard_test_grader import (
    code_touched_files_metrics,
    test_touched_files_metrics,
)
from prbot_msft.swebench_hard.container import RemoteError
from prbot_msft.swebench_hard.example import Example, SetupError
from qstar.common import datapoint, types
from qstar.graders import grader as grader_module
from qstar.graders import taskgen_utils
from qstar.graders.repo_graders_v3.cotograder_mixin import (
    CotogradeRequest,
    CotograderMixin,
    GradeFnOutput,
)
from qstar.graders.repo_graders_v3.rfs_grader_base import close_loop, get_loop
from qstar.sample_completers import sample_completer

Seed = tuple[Any, ...]
logger = structlog.getLogger(__name__)
LOG_FILENAME = "/var/log/supervisor/swebenchhard_repair_comparison_grader_results.log"


def log_wrapper(message: str, instance_id: str):
    return log_to_file_sync(message, instance_id, LOG_FILENAME)


async def log_wrapper_async(message: str, instance_id: str):
    return await log_to_file(message, instance_id, LOG_FILENAME)


def get_prompt_text(sample: types.SampleWithCompletion, patch: str) -> str:
    gt_datapoint: datapoint.Datapoint = sample.gt_datapoint
    issue_description = gt_datapoint.metadata.get("problem_statement")
    silver_patch = (
        gt_datapoint.metadata.get("patch") + "\n" + gt_datapoint.metadata.get("test_patch", "")
    )
    return patch_prompt(problem=issue_description, patch=patch, silver_patch=silver_patch)


def grade_prompt(answer: str) -> GradeFnOutput:
    # return correctness, extra metadata
    try:
        j = json.loads(answer)
        output = GradeFnOutput(
            correct=j.get("rating", "") in {"A+", "A", "A-"},
            extra_metadata={"parsed_answer": json.dumps(j), "rating": j.get("rating", "")},
        )
    except Exception as e:
        output = GradeFnOutput(correct=False, error=e, extra_metadata={"answer": answer})
    return output


@chz.chz(typecheck=True)
class SWEBenchHardRepairComparisonGrader(
    grader_module.Grader[datapoint.HarmonyCompletionDatapoint], CotograderMixin
):
    """Grade SWE-Bench Hard tasks by comparing the student solution with the silver solution."""

    reward_name: str = "sbh_repair_comparison_grader"

    @property
    def loop(self) -> asyncio.AbstractEventLoop:
        return get_loop()

    @classmethod
    def close_loop(cls) -> None:
        return close_loop()

    @final
    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
    ) -> list[types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]]:
        for sample in samples:
            assert isinstance(sample.gt_datapoint, datapoint.HarmonyCompletionDatapoint)

        futures = []
        for sample in samples:
            unique_id = sample.gt_datapoint.unique_id
            assert unique_id is not None
            assert sample.conversation is not None
            async_fut = self._add_patch_to_sample(sample)
            future = asyncio.run_coroutine_threadsafe(async_fut, self.loop)
            futures.append(future)

        samples_with_grade = []
        with ThreadPoolExecutor() as executor:
            for _, future in zip(samples, futures):
                try:
                    sample_with_patch = future.result()
                except Exception as e:
                    error_sample = make_error_sample(
                        sample,
                        e,
                        grader_name="SWEBenchHardRepairComparisonGrader",
                        reward_name=self.reward_name,
                    )
                    samples_with_grade.append(error_sample)
                    continue
                instance_id = sample_with_patch.gt_datapoint.metadata.get("instance_id")
                log_wrapper("Got sample with patch", instance_id)
                grading_future = executor.submit(self._evaluate_code_sync, sample_with_patch)
                graded_sample = grading_future.result()
                log_wrapper("Graded sample with cotograder", instance_id)
                samples_with_grade.append(graded_sample)

            return samples_with_grade

    async def _add_patch_to_sample(
        self,
        sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    ) -> types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]:
        patch = await get_patch(sample=sample)
        sample.ephemeral_metadata["patch"] = patch
        return sample

    def _evaluate_code_sync(
        self,
        sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    ) -> types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]:
        """Sync part: use patch from ephemeral_metadata and call the cotograder."""
        patch = sample.ephemeral_metadata.get("patch", None)
        seed = sample.seed
        instance_id = sample.gt_datapoint.metadata.get("instance_id", None)
        try:
            additional_metrics = {
                **test_touched_files_metrics(sample),
                **code_touched_files_metrics(sample),
            }
            logged_additional_metrics = (
                additional_metrics if sample.metadata.get("initial_sample", True) else {}
            )
            if not patch or patch.strip() == "":
                log_wrapper("ERROR!!! No patch found in sample, marking as incorrect", instance_id)
                sg = sample.with_correctness(
                    reward_name=self.reward_name,
                    is_correct=False,
                    given_answer=None,
                    additional_metadata={
                        "grader": {
                            "comparison_prompt": None,
                            "comparison_response": None,
                            "comparison_score": None,
                            **logged_additional_metrics,
                        },
                    },
                    additional_metrics=logged_additional_metrics,
                )
                return sg
            prompt_text = get_prompt_text(sample, patch)
            log_wrapper("Got prompt texts", instance_id)
            requests = [
                CotogradeRequest(
                    prompt_text=prompt_text,
                    seed=seed,
                    grade_function=grade_prompt,
                )
            ]
            try:
                grades = self.grade_prompt_texts(requests, attempts=5)
            except Exception as e:
                log_wrapper(f"ERROR: Unable to get grades: {e}", instance_id)
                grades = [
                    GradeFnOutput(
                        correct=False, error=e, extra_metadata={"exception": traceback.format_exc()}
                    )
                ]
            log_wrapper(f"GRADE: {grades=}", instance_id)
            grade = grades[0]
            rating = grade.get("grade_fn_metadata", {}).get("rating", "G")
            log_wrapper(f"{rating=}", instance_id)
            for value in ("A+", "A", "A-", "B+", "B", "B-", "C+", "C", "C-", "D+", "D", "D-", "G"):
                additional_metrics[f"rating_{value}"] = int(rating == value)
            additional_metrics["rating"] = rating
            logged_additional_metrics = (
                additional_metrics if sample.metadata.get("initial_sample", True) else {}
            )
            log_wrapper(f"{additional_metrics=}", instance_id)
            # NB: default value of 0 here means don't train on examples which do not modify any test files
            #     should not happen but still just in case
            test_modified_files_recall = additional_metrics.get("test_modified_files_recall", 0)
            sg = sample.with_correctness(
                reward_name=self.reward_name,
                is_correct=grade["correct"] and (test_modified_files_recall > 0),
                given_answer=grade["answer"],
                additional_metadata={
                    "grader": {
                        "comparison_prompt": taskgen_utils.render_grader_system_suffix(
                            grade["prompt_convo"]
                        ),
                        "comparison_response": grade["response"],
                        "comparison_score": 1 if grade["correct"] else 0,
                        "original_metadata": {**sample.gt_datapoint.metadata},
                        **logged_additional_metrics,
                    }
                },
                additional_metrics=logged_additional_metrics,
            )
            return sg
        except Exception as e:
            logger.warning("Flaky CaaS error in SWEBenchHardComparisonGrader", exc_info=True)
            log_wrapper(f"ERROR: Error in grader: {traceback.format_exc()}", instance_id)
            grader_error = GraderError.from_exception(e)
            result = dataclasses.replace(
                sample,
                errors_blamed_on_system=sample.errors_blamed_on_system | {"other_caas_error"},
            ).with_grader_error(grader_error)
            return result


def _retry_callback(retry_state):
    if retry_state.outcome:
        raise retry_state.outcome.exception()  # Raise the last exception
    raise RuntimeError("Unknown error occurred")  # Fallback


@tenacity.retry(
    wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
    stop=tenacity.stop_after_attempt(10),
    before_sleep=tenacity.before_sleep_log(
        logger,  # type: ignore
        logging.WARNING,
    ),
    retry_error_callback=_retry_callback,
)
async def get_patch(*, sample) -> Optional[str]:
    # TODO: this should be re-used by swebenchhard_repair_grader
    container_tool_state = maybe_get_caas_state(sample)
    if not container_tool_state:
        raise ContainerToolStateMissingException(
            "No container tool state, likely due to lack of a model call to the tool"
        )
    instance = get_instance_from_metadata(sample.gt_datapoint.metadata)
    await log_wrapper_async(
        "Using fresh pre_untracked, creating fresh CaaS handle",
        instance.instance_id,
    )

    git_volume_mounts = None
    if instance.version.endswith("-noexec"):
        git_volume_mount_path = f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{instance.repo}/{instance.base_commit}.tar"
        git_volume_mounts = (git_volume_mounts or []) + [
            VolumeMount(
                host=git_volume_mount_path,
                container=git_volume_mount_path,
                deprecated_use_blobfuse=True,
            )
        ]

    await log_wrapper_async(
        f"Creating fresh handle with {instance.image_name=} and {git_volume_mounts=}",
        instance.instance_id,
    )
    try:
        fresh_handle = await CaasHandle.create(
            image_name=instance.image_name, git_volume_mounts=git_volume_mounts
        )
    except Exception as e:
        await log_wrapper_async(
            f"Unable to create fresh handle, {instance.image_name=}", instance.instance_id
        )
        raise FreshCaasException(e) from e
    try:
        pre_untracked = await get_fresh_pre_untracked(fresh_handle, instance)
    except SetupError as e:
        await log_wrapper_async(
            f"Unable to create fresh example to get pre_untracked, {instance.image_name=} Traceback:\n{traceback.format_exc()}",
            instance.instance_id,
        )
        raise FreshExampleException(e) from e

    caas_handle = await setup_container(
        container_tool_state=container_tool_state, git_volume_mounts=git_volume_mounts
    )
    async with caas_handle:
        try:
            ls_root_out = await caas_handle.exec(
                cmd=["ls", "-la", "/"],
                timeout=3600,
                workdir="/",
            )
            with open("/var/log/supervisor/presetup.log", "a+") as f:
                f.write(
                    f"get_patch {instance.instance_id} :ls /:\n{ls_root_out[1].decode('utf-8')}\n"
                )

            ls_workdir_out = await caas_handle.exec(
                cmd=["ls", "-la", "/testbed"],
                timeout=3600,
                workdir="/",
            )
            with open("/var/log/supervisor/presetup.log", "a+") as f:
                f.write(
                    f"get_patch {instance.instance_id} :ls /testbed:\n{ls_workdir_out[1].decode('utf-8')}\n"
                )

            example = await Example.create(
                swe_datum=instance,
                container_handle=caas_handle,
                timeout=3_600_000,
                run_setup=False,
                run_tests=False,
            )
        except SetupError as e:
            await log_wrapper_async(
                f"Unable to create example, re-using container_tool_state. Traceback:\n{traceback.format_exc()}",
                instance.instance_id,
            )
            raise ReuseCaasException(e) from e
        await update_example_with_pre_untracked(example, pre_untracked)
        try:
            patch = await example.git_diff(include_new_untracked=True, timeout=60_000)
        except RemoteError as e:
            await log_wrapper_async(
                f"Unable to get patch for {instance.instance_id} re-using container_tool_state. Traceback:\n{traceback.format_exc()}",
                instance.instance_id,
            )
            patch = None
        await log_wrapper_async(
            f"get_patch {instance.instance_id}: Got patch:\n{patch if patch else 'None'}",
            instance.instance_id,
        )
    return patch
