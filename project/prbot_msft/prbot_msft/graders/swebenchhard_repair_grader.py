import dataclasses
import json
import traceback
from typing import Tuple

import chz
import structlog
from berry.sample import GraderError
from caas.protocol import VolumeMount
from prbot_msft.caas_handle_utils import <PERSON><PERSON>sHandle
from prbot_msft.graders.swebenchhard_grader_base import (
    SWEBenchHardGraderBase,
    get_fresh_pre_untracked,
    get_instance_from_metadata,
    handle_error,
    log_to_file,
    maybe_get_caas_state,
    setup_container,
    update_example_with_pre_untracked,
)
from prbot_msft.graders.swebenchhard_test_grader import (
    code_touched_files_metrics,
    test_touched_files_metrics,
)
from prbot_msft.swebench_hard.container import RemoteError
from prbot_msft.swebench_hard.example import Example
from prbot_msft.swebench_hard.stream import SweDatum
from qstar.common import datapoint, types

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class SWEBenchHardRepairGrader(SWEBenchHardGraderBase):

    use_fresh_pre_untracked: bool = True
    reward_name: str = "swebenchhard_repair_grader"

    async def _evaluate_code(
        self,
        sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    ) -> types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]:
        try:
            container_tool_state = maybe_get_caas_state(sample)
            if not container_tool_state:
                await log_to_file(
                    "No tool state: returning is_correct=False", instance_id=instance.instance_id
                )
                return sample.with_correctness(
                    reward_name=self.reward_name,
                    is_correct=False,
                    additional_metadata={
                        "grade_metadata": {
                            "error": "No container tool state, likely due to lack of a model call to the tool",
                        },
                        "original_metadata": {**sample.gt_datapoint.metadata},
                    },
                )
            # grab a fresh container to get pre_untracked
            instance = get_instance_from_metadata(sample.gt_datapoint.metadata)
            if self.use_fresh_pre_untracked:
                await log_to_file(
                    "Using fresh pre_untracked, creating fresh CaaS handle",
                    instance_id=instance.instance_id,
                )

                git_volume_mounts = None
                if instance.version.endswith("-noexec"):
                    git_volume_mount_path = f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{instance.repo}/{instance.base_commit}.tar"
                    git_volume_mounts = (git_volume_mounts or []) + [
                        VolumeMount(
                            host=git_volume_mount_path,
                            container=git_volume_mount_path,
                            deprecated_use_blobfuse=True,
                        )
                    ]

                try:
                    fresh_handle = await CaasHandle.create(
                        image_name=instance.image_name, git_volume_mounts=git_volume_mounts
                    )
                except Exception as e:
                    await log_to_file(f"Unable to create fresh handle", instance.instance_id)
                    logger.warning(
                        "Flaky CaaS error in SWEBenchHardRepairGrader, unable to create fresh handle",
                        exc_info=True,
                    )
                    grader_error = GraderError.from_exception(e)
                    return dataclasses.replace(
                        sample,
                        errors_blamed_on_system=sample.errors_blamed_on_system
                        | {"fresh_caas_error"},
                    ).with_grader_error(grader_error)

                pre_untracked = await get_fresh_pre_untracked(fresh_handle, instance)
            # now grade with the rolled out caas handle
            caas_handle = await setup_container(container_tool_state=container_tool_state)
            async with caas_handle:
                example = await Example.create(
                    swe_datum=instance,
                    container_handle=caas_handle,
                    timeout=3_600_000,
                    run_setup=False,
                    run_tests=False,
                )
                if self.use_fresh_pre_untracked:
                    await update_example_with_pre_untracked(example, pre_untracked)
                patch, outcome = await compute_outcome(instance=instance, example=example)
                sample.ephemeral_metadata["patch"] = patch
                await log_to_file(
                    f"{json.dumps({'instance': instance.instance_id, **outcome})}",
                    instance_id=instance.instance_id,
                )
            if outcome.get("error"):
                logger.warning("Flaky CaaS error in SWEBenchHardRepairGrader", exc_info=True)
                grader_error = GraderError.from_exception(e)
                return [
                    dataclasses.replace(
                        sample,
                        errors_blamed_on_system=sample.errors_blamed_on_system | {"caas_error"},
                    ).with_grader_error(grader_error)
                ]
            else:
                additional_metrics = {
                    **test_touched_files_metrics(sample),
                    **code_touched_files_metrics(sample),
                }
                logged_additional_metrics = (
                    additional_metrics if sample.metadata.get("initial_sample", True) else {}
                )
                return sample.with_correctness(
                    reward_name=self.reward_name,
                    is_correct=outcome.get("resolved", False),
                    given_answer=patch,
                    additional_metadata={
                        "grader_outcome": {**outcome},
                        "original_metadata": {**sample.gt_datapoint.metadata},
                        **logged_additional_metrics,
                    },
                    additional_metrics=logged_additional_metrics,
                )
        except Exception as e:
            log_to_file(
                f"Instance {instance.instance_id}, exception in SWEBenchHardRepairGrader, Traceback:\n{traceback.format_exc()}",
                instance.instance_id,
            )
            return handle_error(sample, e, self.reward_name)


async def compute_outcome(*, instance: SweDatum, example: Example) -> Tuple[str, dict[str, int]]:
    try:
        patch = await example.git_diff(include_new_untracked=True, timeout=60_000)
    except RemoteError as e:
        patch = None
        outcome = {"error": 1, "exception": str(e), "traceback": traceback.format_exc()}
    else:
        if not patch or not patch.strip():
            outcome = {"resolved": False, "reason": "No patch"}
        else:
            git_volume_mounts = None
            if instance.version.endswith("-noexec"):
                git_volume_mount_path = f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{instance.repo}/{instance.base_commit}.tar"
                git_volume_mounts = (git_volume_mounts or []) + [
                    VolumeMount(
                        host=git_volume_mount_path,
                        container=git_volume_mount_path,
                        deprecated_use_blobfuse=True,
                    )
                ]

            async with await CaasHandle.create(
                image_name=instance.image_name, git_volume_mounts=git_volume_mounts
            ) as fresh_handle:
                fresh = await Example.create(
                    swe_datum=instance, container_handle=fresh_handle, timeout=3_600_000
                )
                try:
                    outcome = await fresh.eval_report(patch=patch, timeout=3_600_000)
                except Exception as e:
                    outcome = {
                        "bad_code": 1,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }

    return patch, outcome
