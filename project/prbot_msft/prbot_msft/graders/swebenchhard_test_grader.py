import dataclasses
import json
import re
import traceback
from typing import Tuple

import chz
import structlog
from berry.sample import GraderError
from caas.protocol import VolumeMount
from prbot_msft.caas_handle_utils import <PERSON><PERSON>sHandle
from prbot_msft.graders.swebenchhard_grader_base import (
    SWEBenchHardGraderBase,
    get_fresh_pre_untracked,
    get_instance_from_metadata,
    handle_error,
    log_to_file,
    log_to_file_sync,
    maybe_get_caas_state,
    setup_container,
    update_example_with_pre_untracked,
)
from prbot_msft.swebench_hard.container import RemoteError
from prbot_msft.swebench_hard.example import Example
from prbot_msft.swebench_hard.stream import SweDatum
from qstar.common import datapoint, types

LOG_FILENAME = "/var/log/supervisor/swebenchhard_test_grader_results.log"

logger = structlog.stdlib.get_logger(component=__name__)


def log_wrapper(message: str, instance_id: str):
    return log_to_file_sync(message, instance_id, LOG_FILENAME)


def get_modified_files(patch: str, instance_id: str) -> set[str]:
    """
    Parse a unified git diff and return the set of modified file paths.

    Examples of diff lines handled:
      diff header:
        diff --git a/path/to/foo.txt b/path/to/foo.txt
      rename:
        rename from old name with spaces.txt
        rename to new name with spaces.txt
      deletion:
        diff --git a/bar.txt b/dev/null
      file headers:
        --- a/src/baz.py
        +++ b/src/baz.py

    - Uses regex to handle paths with spaces
    - Captures renames (rename from/to)
    - Records deletions by including the old path when new is /dev/null
    - Falls back on '---' and '+++' headers if needed
    """
    modified: set[str] = set()
    diff_re = re.compile(r"^diff --git a/(?P<old>.*) b/(?P<new>.*)$")
    for line in patch.splitlines():
        # Git diff header
        m = diff_re.match(line)
        if m:
            old_path = m.group("old")
            new_path = m.group("new")
            if new_path == "/dev/null":
                modified.add(old_path)
            else:
                modified.add(new_path)
            continue
        # Rename metadata
        if line.startswith("rename from "):
            modified.add(line[len("rename from ") :])
            continue
        if line.startswith("rename to "):
            modified.add(line[len("rename to ") :])
            continue
        # Fallback on file headers
        if line.startswith("--- ") or line.startswith("+++ "):
            _, path = line.split(" ", 1)
            if path not in ("/dev/null", "a/dev/null", "b/dev/null"):
                if path.startswith("a/") or path.startswith("b/"):
                    path = path[2:]
                modified.add(path)
    log_wrapper(f"Patch modifies: {sorted(modified)}", instance_id=instance_id)
    return modified


def touched_files_metrics(
    sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    gold_patch,
    agent_patch,
    prefix="modified_files",
):
    gold_modified_files = get_modified_files(
        gold_patch,
        sample.gt_datapoint.metadata.get("instance_id", ""),
    )
    modified_files = get_modified_files(
        agent_patch,
        sample.gt_datapoint.metadata.get("instance_id", ""),
    )
    recall = (
        len(modified_files & gold_modified_files) / len(gold_modified_files)
        if gold_modified_files
        else 0.0
    )
    metrics = {
        f"{prefix}_recall": recall,
        f"{prefix}_precision": len(modified_files & gold_modified_files) / len(modified_files)
        if modified_files
        else 0.0,
        f"{prefix}_perfect": recall == 1.0,
    }
    log_wrapper(
        f"Metrics: {metrics}, gold: {gold_modified_files}, modified: {modified_files}",
        instance_id=sample.gt_datapoint.metadata.get("instance_id", ""),
    )
    return metrics


def test_touched_files_metrics(
    sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
):
    return touched_files_metrics(
        sample,
        gold_patch=sample.gt_datapoint.metadata.get("test_patch", ""),
        agent_patch=sample.ephemeral_metadata.get("patch", ""),
        prefix="test_modified_files",
    )


def code_touched_files_metrics(
    sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
):
    return touched_files_metrics(
        sample,
        gold_patch=sample.gt_datapoint.metadata.get("patch", ""),
        agent_patch=sample.ephemeral_metadata.get("patch", ""),
        prefix="code_modified_files",
    )


@chz.chz(typecheck=True)
class SWEBenchHardTestGrader(SWEBenchHardGraderBase):

    use_fresh_pre_untracked: bool = True
    reward_name: str = "swebenchhard_test_grader"

    async def _evaluate_code(
        self,
        sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    ) -> types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]:
        try:
            container_tool_state = maybe_get_caas_state(sample)
            if not container_tool_state:
                await log_to_file(
                    "No tool state: returning is_correct=False", instance_id=instance.instance_id
                )
                return sample.with_correctness(
                    reward_name=self.reward_name,
                    is_correct=False,
                    additional_metadata={
                        "grade_metadata": {
                            "error": "No container tool state, likely due to lack of a model call to the tool",
                        },
                        "original_metadata": {**sample.gt_datapoint.metadata},
                    },
                )
            # grab a fresh container to get pre_untracked
            instance = get_instance_from_metadata(sample.gt_datapoint.metadata)
            if self.use_fresh_pre_untracked:
                await log_to_file(
                    "Using fresh pre_untracked, creating fresh CaaS handle",
                    instance_id=instance.instance_id,
                )

                git_volume_mounts = None
                if instance.version.endswith("-noexec"):
                    git_volume_mount_path = f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{instance.repo}/{instance.base_commit}.tar"
                    git_volume_mounts = (git_volume_mounts or []) + [
                        VolumeMount(
                            host=git_volume_mount_path,
                            container=git_volume_mount_path,
                            deprecated_use_blobfuse=True,
                        )
                    ]

                try:
                    fresh_handle = await CaasHandle.create(
                        image_name=instance.image_name, git_volume_mounts=git_volume_mounts
                    )
                except Exception as e:
                    await log_to_file(f"Unable to create fresh handle", instance.instance_id)
                    logger.warning(
                        "Flaky CaaS error in SWEBenchHardTestGrader, unable to create fresh handle",
                        exc_info=True,
                    )
                    grader_error = GraderError.from_exception(e)
                    return dataclasses.replace(
                        sample,
                        errors_blamed_on_system=sample.errors_blamed_on_system
                        | {"fresh_caas_error"},
                    ).with_grader_error(grader_error)

                pre_untracked = await get_fresh_pre_untracked(fresh_handle, instance)
            # now grade with the rolled out caas handle
            caas_handle = await setup_container(container_tool_state=container_tool_state)
            async with caas_handle:
                example = await Example.create(
                    swe_datum=instance,
                    container_handle=caas_handle,
                    timeout=3_600_000,
                    run_setup=False,
                    run_tests=False,
                )
                if self.use_fresh_pre_untracked:
                    await update_example_with_pre_untracked(example, pre_untracked)
                patch, outcome = await compute_outcome(instance=instance, example=example)
                sample.ephemeral_metadata["patch"] = patch
                additional_metrics = touched_files_metrics(sample)
                logged_additional_metrics = (
                    additional_metrics if sample.metadata.get("initial_sample", True) else {}
                )
                await log_to_file(
                    f"{json.dumps({'instance': instance.instance_id, **outcome})}",
                    instance_id=instance.instance_id,
                )
            if outcome.get("error"):
                logger.warning("Flaky CaaS error in SWEBenchHardTestGrader", exc_info=True)
                grader_error = GraderError.from_exception(e)
                return [
                    dataclasses.replace(
                        sample,
                        errors_blamed_on_system=sample.errors_blamed_on_system | {"caas_error"},
                    ).with_grader_error(grader_error)
                ]
            else:
                return sample.with_correctness(
                    reward_name=self.reward_name,
                    is_correct=outcome.get("resolved", False),
                    given_answer=patch,
                    additional_metadata={
                        "grader_outcome": {**outcome},
                        "original_metadata": {**sample.gt_datapoint.metadata},
                        **logged_additional_metrics,
                    },
                    additional_metrics=logged_additional_metrics,
                )
        except Exception as e:
            log_to_file(
                f"Instance {instance.instance_id}, exception in SWEBenchHardTestGrader, Traceback:\n{traceback.format_exc()}",
                instance.instance_id,
            )
            return handle_error(sample, e, self.reward_name)


async def compute_outcome(*, instance: SweDatum, example: Example) -> Tuple[str, dict[str, int]]:
    try:
        patch = await example.git_diff(include_new_untracked=True, timeout=60_000)
    except RemoteError as e:
        patch = None
        outcome = {"error": 1, "exception": str(e), "traceback": traceback.format_exc()}
    else:
        if not patch or not patch.strip():
            outcome = {"resolved": False, "reason": "No patch"}
        else:
            git_volume_mounts = None
            if instance.version.endswith("-noexec"):
                git_volume_mount_path = f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{instance.repo}/{instance.base_commit}.tar"
                git_volume_mounts = (git_volume_mounts or []) + [
                    VolumeMount(
                        host=git_volume_mount_path,
                        container=git_volume_mount_path,
                        deprecated_use_blobfuse=True,
                    )
                ]

            async with await CaasHandle.create(
                image_name=instance.image_name, git_volume_mounts=git_volume_mounts
            ) as fresh_handle:
                fresh = await Example.create(
                    swe_datum=instance, container_handle=fresh_handle, timeout=3_600_000
                )
                try:
                    outcome = await fresh.eval_test_report(test_patch=patch, timeout=3_600_000)
                    base_report_status = fresh.base_report["tests_status"]
                    patched_report_status = outcome["tests_status"]
                    outcome["resolved"] = (
                        base_report_status["FAIL_TO_PASS"]["success"]
                        < patched_report_status["FAIL_TO_PASS"]["success"]
                        and base_report_status["FAIL_TO_PASS"]["failure"]
                        >= patched_report_status["FAIL_TO_PASS"]["failure"]
                    )
                except Exception as e:
                    outcome = {
                        "bad_code": 1,
                        "exception": str(e),
                        "traceback": traceback.format_exc(),
                    }

    return patch, outcome
