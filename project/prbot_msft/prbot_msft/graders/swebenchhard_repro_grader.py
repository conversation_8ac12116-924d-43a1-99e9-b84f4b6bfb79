import json
import traceback

import chz
import structlog
from caas.protocol import VolumeMount
from prbot_msft.caas_handle_utils import <PERSON><PERSON>s<PERSON><PERSON><PERSON>
from prbot_msft.graders.exceptions import (
    ContainerToolStateMissingException,
    FreshCaasException,
    FreshExampleException,
    ReuseCaasException,
)
from prbot_msft.graders.swebenchhard_grader_base import (
    SWEBenchHardGraderBase,
    get_fresh_pre_untracked,
    get_instance_from_metadata,
    log_to_file,
    maybe_get_caas_state,
    setup_container,
    update_example_with_pre_untracked,
)
from prbot_msft.swebench_hard.container import RemoteError
from prbot_msft.swebench_hard.example import Example, SetupError
from prbot_msft.swebench_hard.repro import Repro
from prbot_msft.swebench_hard.stream import SweDatum
from qstar.common import datapoint, types

logger = structlog.stdlib.get_logger(component=__name__)

TIMEOUT = 3_600_000
LOG_FILENAME = "/var/log/supervisor/swebenchhard_repro_grader_results.log"


async def log_wrapper(message: str, instance_id: str):
    await log_to_file(message, instance_id, LOG_FILENAME)


@chz.chz(typecheck=True)
class SWEBenchHardReproGrader(SWEBenchHardGraderBase):
    reward_name: str = "swebenchhard_repro_grader"

    async def _evaluate_code(
        self,
        sample: types.SampleWithCompletion[datapoint.HarmonyCompletionDatapoint],
    ) -> types.SampleWithGrade[datapoint.HarmonyCompletionDatapoint]:
        instance = get_instance_from_metadata(sample.gt_datapoint.metadata)
        await log_wrapper("Entered evaluate code", instance_id=instance.instance_id)
        container_tool_state = maybe_get_caas_state(sample)
        if not container_tool_state:
            await log_wrapper(
                "No tool state: returning is_correct=False", instance_id=instance.instance_id
            )
            return sample.with_correctness(
                reward_name=self.reward_name,
                is_correct=False,
                additional_metadata={
                    "grade_metadata": {
                        "error": "No container tool state, likely due to lack of a model call to the tool",
                    },
                    "original_metadata": {**sample.gt_datapoint.metadata},
                },
            )

        git_volume_mounts = None
        if instance.version.endswith("-noexec"):
            git_volume_mount_path = f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{instance.repo}/{instance.base_commit}.tar"
            git_volume_mounts = (git_volume_mounts or []) + [
                VolumeMount(
                    host=git_volume_mount_path,
                    container=git_volume_mount_path,
                    deprecated_use_blobfuse=True,
                )
            ]

        try:
            fresh_handle = await CaasHandle.create(
                image_name=instance.image_name, git_volume_mounts=git_volume_mounts
            )
        except Exception as e:
            await log_wrapper(f"Unable to create fresh handle", instance_id=instance.instance_id)
            raise FreshCaasException(e) from e
        try:
            pre_untracked = await get_fresh_pre_untracked(
                fresh_handle=fresh_handle,
                instance=instance,
            )
        except SetupError as e:
            await log_wrapper(
                f"Unable to create fresh example to get pre_untracked",
                instance_id=instance.instance_id,
            )
            raise FreshExampleException(e) from e

        caas_handle = await setup_container(container_tool_state)
        async with caas_handle:
            try:
                example = await Example.create(
                    swe_datum=instance,
                    container_handle=caas_handle,
                    timeout=TIMEOUT,
                    run_setup=False,
                    run_tests=False,
                )
            except SetupError as e:
                await log_wrapper(
                    f"Unable to create example, re-using container_tool_state",
                    instance_id=instance.instance_id,
                )
                raise ReuseCaasException(e) from e
            await update_example_with_pre_untracked(example, pre_untracked)
            await log_wrapper(
                f"Created example with {example.pre_untracked}, about to compute outcome\n",
                instance_id=instance.instance_id,
            )
            outcome = await compute_outcome(instance=instance, example=example, timeout=TIMEOUT)
            await log_wrapper(
                f"{json.dumps({'instance': instance.instance_id, **outcome})}",
                instance_id=instance.instance_id,
            )
            sample.ephemeral_metadata["repro"] = outcome.get("repro", None)
        return sample.with_correctness(
            reward_name=self.reward_name,
            is_correct=outcome.get("resolved", False),
            given_answer=outcome.get("repro", None),
            additional_metadata={
                "grader": {**outcome},
                "original_metadata": {**sample.gt_datapoint.metadata},
            },
        )


async def compute_outcome(*, instance: SweDatum, example: Example, timeout: int) -> dict[str, int]:
    try:
        repro = await Repro.create_from_container_state(
            example.container_handle,
            workdir=example.repo_directory,
            timeout=timeout,
            ignore_files=example.pre_untracked | {"run_tests.sh"},
            base_commit=instance.base_commit,
        )
    except Exception as e:
        logger.error(f"Failed to collect repro artifact: {e}")
        logger.info(traceback.format_exc())
        repro = None
        outcome = {
            "no_repro": 1,
            "exception": str(e),
            "traceback": traceback.format_exc(),
            "reproduced": False,
            "resolved": False,
        }
    else:
        git_volume_mounts = None
        if instance.version.endswith("-noexec"):
            git_volume_mount_path = f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{instance.repo}/{instance.base_commit}.tar"
            git_volume_mounts = (git_volume_mounts or []) + [
                VolumeMount(
                    host=git_volume_mount_path,
                    container=git_volume_mount_path,
                    deprecated_use_blobfuse=True,
                )
            ]

        handle = await CaasHandle.create(
            image_name=instance.image_name, git_volume_mounts=git_volume_mounts
        )
        fresh = await Example.create(
            swe_datum=instance, container_handle=handle, run_tests=False, timeout=TIMEOUT
        )
        async with fresh.container_handle:
            await repro.upload_to_container(
                fresh.container_handle, workdir=fresh.repo_directory, timeout=timeout
            )
            try:
                reproduced_output = await repro.run_on_container(
                    fresh.container_handle, workdir=fresh.repo_directory, timeout=timeout
                )
                reproduced = reproduced_output.strip() == "Issue reproduced"
            except RemoteError as e:
                reproduced_output, reproduced = str(e), False
            if reproduced:
                await fresh.container_handle.upload_file(
                    remote_filename="/code_patch.patch", contents=fresh.swe_datum["patch"].encode()
                )
                await fresh.container_handle.exec(
                    cmd=["git", "apply", "-v", "--reject", "/code_patch.patch"],
                    workdir=fresh.repo_directory,
                    timeout=timeout,
                )

                try:
                    resolved_output = await repro.run_on_container(
                        fresh.container_handle, workdir=fresh.repo_directory, timeout=timeout
                    )
                    resolved = resolved_output.strip() == "Issue resolved"
                except RemoteError as e:
                    resolved_output, resolved = str(e), False
            else:
                resolved_output, resolved = None, False

            outcome = {
                "reproduced": reproduced,
                "reproduced_output": reproduced_output,
                "resolved": resolved,
                "resolved_output": resolved_output,
                "repro": None if repro is None else repro.to_dict(),
                "no_repro": 0,
            }
            if reproduced:
                outcome["resolved_given_reproduced"] = resolved

    return outcome
