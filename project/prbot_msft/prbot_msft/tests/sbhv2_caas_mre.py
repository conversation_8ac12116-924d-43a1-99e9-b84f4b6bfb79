from caas.commands import DownloadFileFromContainer, RawExec, UploadFile
from caas.protocol import NetworkMode, VolumeMount
from prbot_msft.caas_handle_utils import C<PERSON>sHandle
from prbot_msft.graders.swebenchhard_repair_grader import get_fresh_pre_untracked
from prbot_msft.swebench_hard.example import Example
from prbot_msft.swebench_hard.stream import SweDatum


async def main():
    obj = dict(
        instance_id="intel__intel-xpu-backend-for-triton-3392",
        repo="intel/intel-xpu-backend-for-triton",
        base_commit="caf96327bf9767e85e06968dc547cf253dcd0e25",
        version="sbhv2-23482-noexec",
        split_type="Full",
        problem_statement="Add LLVM SPIR-V backend-based CI functionality checks\nFor the purpose of SPIR-V backend enabling, we need to run functionality tests with the modified dependencies. The requirement for this is to build llvm with an additional flag `-DLLVM_EXPERIMENTAL_TARGETS_TO_BUILD=SPIRV`.\n\nThe switch between the backend and the translator will be enabled by an environment variable.",
        hints_text="",
        patch="",
        test_patch="",
        setup_time=0.013844013214111328,
        setup_script=None,
        test_script=None,
        initial_gitdiff=None,
        filenames=[],
        language_count=[],
        tests_results_patched=None,
        tests_results=None,
        fail_to_pass=None,
        pass_to_pass=None,
        other_to_other=None,
        image_name=None,
    )
    instance = SweDatum(**obj)

    git_volume_mount_path = None
    git_volume_mounts = None
    if instance.version.endswith("-noexec"):
        repo = instance.repo
        commit = instance.base_commit
        # Mount the git repo as a volume
        git_volume_mount_path = (
            f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{repo}/{commit}.tar"
        )
        git_volume_mounts = (git_volume_mounts or []) + [
            VolumeMount(
                host=git_volume_mount_path,
                container=git_volume_mount_path,
                deprecated_use_blobfuse=True,
            )
        ]
        print(f"Mounting git volume for {repo}@{commit}, {git_volume_mounts=}")

    try:
        fresh_handle = await CaasHandle.create(
            image_name=instance.image_name, git_volume_mounts=git_volume_mounts
        )
    except Exception as e:
        print(f"Unable to create fresh handle, {instance.image_name=}")
        raise e

    original_example = await Example.create(
        swe_datum=instance,
        container_handle=fresh_handle,
        timeout=900_000,
        run_setup=instance.version.endswith("-noexec"),
        run_tests=False,
    )

    ls_testbed = await fresh_handle.exec(["ls", "-la", "/testbed"], timeout=600, workdir="/")
    print(f"ls /testbed:\nEC:{ls_testbed[0]}\nOUT:{ls_testbed[1].decode('utf-8')}")

    git_log = await fresh_handle.exec(
        ["git", "log"],
        timeout=600,
        workdir="/testbed",
    )
    print(f"git log:\nEC:{git_log[0]}\nOUT:{git_log[1].decode('utf-8')}")

    git_status = await fresh_handle.exec(
        ["git", "status"],
        timeout=600,
        workdir="/testbed",
    )
    print(f"git status:\nEC:{git_status[0]}\nOUT:{git_status[1].decode('utf-8')}")

    await fresh_handle.exec(
        ["bash", "-c", "echo 'Hello, World!' > /testbed/hello.txt"],
        timeout=600,
        workdir="/testbed",
    )

    git_add = await fresh_handle.exec(
        ["git", "add", "."],
        timeout=600,
        workdir="/testbed",
    )
    print(f"git add:\nEC:{git_add[0]}\nOUT:{git_add[1].decode('utf-8')}")

    git_commit = await fresh_handle.exec(
        ["git", "commit", "-m", "Added hello.txt"],
        timeout=600,
        workdir="/testbed",
    )
    print(f"git commit:\nEC:{git_commit[0]}\nOUT:{git_commit[1].decode('utf-8')}")

    git_log_after_commit = await fresh_handle.exec(
        ["git", "log"],
        timeout=600,
        workdir="/testbed",
    )
    print(
        f"git log after commit:\nEC:{git_log_after_commit[0]}\nOUT:{git_log_after_commit[1].decode('utf-8')}"
    )

    git_diff = await original_example.git_diff(include_new_untracked=True, timeout=600)
    print(f"git diff:\n{git_diff}")


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
