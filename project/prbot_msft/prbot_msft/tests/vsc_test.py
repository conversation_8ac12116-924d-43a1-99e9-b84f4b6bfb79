import json
import os
import time

import structlog
import tenacity
from berry_caas_container_tool.caas_container_tool import _get_new_terminal_session
from caas.commands import HttpGet, HttpPost, RawExec
from caas.internal.errors import ClientError, ExecNetworkError
from caas.protocol import NetworkMode, SandboxRuntime, VolumeMount
from caas_tool.caas_container import CaasContainer, CaasSession
from deep_swe_msft.tools.vscode_copilot_tool import MAX_RETRY_SECONDS, VSC_MNT, setup_vscutils
from prbot_msft.configs.vsc_caas_container_tool import (
    SweBenchHardVSCCaasContainerResourceConfig,
    VSCodeTool,
)
from qstar.common.datapoint import HarmonyCompletionDatapoint, HarmonyDatapoint

logger = structlog.stdlib.get_logger(component=__name__)


@tenacity.retry(
    stop=tenacity.stop_after_delay(MAX_RETRY_SECONDS),
    wait=tenacity.wait_exponential_jitter(initial=2, exp_base=2),
    reraise=True,
    retry=tenacity.retry_if_not_exception_type(ClientError),
)
async def new_container(caas_endpoint: str, caas_image: str) -> CaasContainer:
    """
    Create a new container for the VSCode Copilot tool.
    """
    t0 = time.time()
    logger.info(f"Creating caas container at endpoint: {caas_endpoint} image: {caas_image}...")

    # tmpfs = [Tmpfs(container=TOOL_LOCAL_DIR, size="4g")]
    container = await CaasContainer.new(
        caas_endpoint=caas_endpoint,
        image_name=caas_image,
        volume_mounts=VSC_MNT,
        # cannot use tmpfs because of the error:
        # > copilot-chat@0.27.0 test:extension
        # > xvfb-run -a --server-args="-screen 0 1280x720x24" vscode-test
        # /usr/bin/xvfb-run: 184: vscode-test: Permission denied
        # tried chmod doesn't work
        # tmpfs=tmpfs,
        memory_limit="32g",
        cpu_limit="16.0",
        network=NetworkMode.NONE,
    )
    logger.info(
        f"Container created in {time.time() - t0:.2f} seconds. caas_session_state: {container.caas_session_state}"
    )

    return container


async def main():
    os.makedirs("/var/log/supervisor", exist_ok=True)
    caas_endpoint: str = "https://eastus2.caas.azure.com"
    caas_container_image: str = (
        "acrcommitcaaseastus2ame.azurecr.io/swe-bench-hard-depromeet__street-drop-server-35:v3"
    )

    # # Create a new container
    caas_container = await new_container(caas_endpoint, caas_container_image)

    # # Run a command in the container
    result = await caas_container.exec(
        cmd=["echo", "'Hello, World!'"],
        workdir="/",
        timeout=3600,
        env=None,
    )
    print(result[1].decode())

    await setup_vscutils(
        datapoint={"metadata": {"instance_id": "vsc_test"}},
        terminal_session=caas_container.terminal_session,
        workdir="/testbed",
    )

    dp = HarmonyCompletionDatapoint("test")
    dp.metadata["instance_id"] = "test_instance"
    dp.metadata[
        "image_name"
    ] = "acrcommitcaaseastus2ame.azurecr.io/swe-bench-hard-depromeet__street-drop-server-35:v3"
    rc = SweBenchHardVSCCaasContainerResourceConfig(setup_fn=setup_vscutils)
    caas_container = await rc.initialize_resource(dp)

    # This is test file to be run on CPU node for sanity
    # This block allows to test starting from terminal session
    # instead of creating full config and datapoint
    # helps with debugging and isolating issues

    # terminal_session = await _get_new_terminal_session(
    #     caas_endpoint=caas_endpoint,
    #     caas_container_image=caas_container_image,
    #     cpu_limit=str(8),
    #     memory_limit=f"16g",
    #     idle_ttl=3600,
    #     num_gpus=None,
    #     network=None,
    #     volume_mounts=VSC_MNT,
    #     sandbox_runtime=SandboxRuntime.UNSAFE,
    #     env=None,
    #     cmd=None,
    # )
    await setup_vscutils(
        datapoint={"metadata": {"instance_id": "vsc_test"}},
        terminal_session=caas_container.terminal_session,
        workdir="/testbed",
    )

    tool = VSCodeTool(
        container=caas_container,
        terminal_emulator_path="teammate_service.termites.lean_terminal:LeanTerminal",
        exec_output_processor_path="teammate_service.post_processors.truncate:TruncateExecPostProcessor",
        max_execution_time=3_600_000,
        tool_penalty_multiplier=1,
        max_tool_penalty=0.5,
        default_exec_timeout=1_800_000,
    )

    async for value in tool.run_in_terminal(
        command="echo 'Hello, World!'",
        explanation="This is a test command",
        isBackground=False,
    ):
        print(value)

    # Clean up the container
    await caas_container.delete()


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
