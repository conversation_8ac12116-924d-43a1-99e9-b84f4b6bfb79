from typing import Any

from caas_tool.caas_container import CaasSession
from deep_swe_msft.tools.get_coreutils import setup_coreutils as setup_padawan_tools_v2
from prbot_msft.swebench_hard.example import REPO_DIRECTORY


async def setup_fn_padawan_v2(
    *, datapoint: dict[str, Any], session: CaasSession, workdir: str | None
) -> None:
    version = datapoint["metadata"].get("version", "sbh-12878")
    install_node = version.endswith("-noexec")
    await setup_padawan_tools_v2(
        session=session,
        datapoint=datapoint,
        repo_root=REPO_DIRECTORY,
        install_node=install_node,
        add_package_json=True,
        skip_tool_packages=install_node,
    )
