import asyncio
import dataclasses
import functools
import json
import logging
from textwrap import dedent
from typing import Any, Protocol

import caas
import chz
import httpx
import structlog
import tenacity
from berry_caas_container_tool.caas_container_tool import (
    BerryCaasContainerTool as OriginalBerryCaasContainerTool,
)
from berry_caas_container_tool.caas_container_tool import CaasContainerResourceConfig
from berry_caas_container_tool.caas_container_tool import (
    CaasContainerToolConfig as OriginalCaasContainerToolConfig,
)
from berry_caas_container_tool.caas_container_tool import (
    _get_new_terminal_session_and_run_setup,
    create_caas_tool_plugins,
)
from berry_harmony.tools.berry_tool_interface import BerryTool
from caas.api import CaasSession
from caas.commands import DownloadFileFromContainer, RawExec, UploadFile
from caas.protocol import NetworkMode, VolumeMount
from caas.terminal.api import TerminalSession
from caas_tool.caas_container import CaasContainer
from chz.validators import gt, typecheck
from deep_swe_msft.tools.caas_container_tool import (
    DeepSWECaasContainerTool,
    DeepSWECaasContainerToolConfig,
)
from deep_swe_msft.tools.vscode_copilot_tool import VSC_MNT
from prbot_msft.swebench_hard.example import initialize_resource
from prbot_msft.terminal_session_handle_utils import TerminalSessionHandle
from qstar.common import datapoint
from qstar.common.datapoint import HarmonyDatapoint
from qstar.common.tools import azure_sas_token

logger = structlog.get_logger(component=__name__)


def log_with_timestamp(message: str, instance_id: str = None):
    from datetime import datetime

    with open(f"/var/log/supervisor/swebenchhard_caas_resource_config.log", "a+") as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}][{instance_id}]: {message}\n")


class LeakyBucket:
    def __init__(self, rate_limit: float, max_occupancy: int):
        self.interval = 1.0 / rate_limit
        self.max_occupancy = max_occupancy
        self.sem = asyncio.BoundedSemaphore(max_occupancy)
        self.refill_task = asyncio.create_task(self._refill_loop())

    async def _refill_loop(self):
        try:
            while True:
                try:
                    self.sem.release()
                except ValueError:
                    pass
                await asyncio.sleep(self.interval)
        except asyncio.CancelledError:
            return

    async def __aenter__(self):
        await self.sem.acquire()

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        return False


@functools.cache
def leaky_bucket(rate_limit, max_occupancy):
    return LeakyBucket(rate_limit=rate_limit, max_occupancy=max_occupancy)


class SweBenchHardSetupFn(Protocol):
    async def __call__(
        self, *, datapoint: dict[str, Any], session: CaasSession, workdir: str | None
    ) -> None:
        ...


@chz.chz
class SweBenchHardCaasContainerResourceConfig(CaasContainerResourceConfig):
    caas_cpu_limit: float = 16.0
    caas_memory_limit: int = 64
    caas_idle_ttl: int = 3_600
    caas_network: NetworkMode | None = chz.field(default=None)
    enable_network_after_setup: bool = False
    caas_endpoint: str = "https://eastus2.caas.azure.com"
    workdir = "/testbed"
    run_test_script = "run_tests.sh"
    rate_limit: float = chz.field(
        default=10.0,
        doc="Rate limit for the leaky bucket in tokens/sec.",
        validator=[typecheck, gt(0)],
    )
    max_occupancy: int = chz.field(
        default=1, doc="Max occupancy for the leaky bucket.", validator=[typecheck, gt(0)]
    )
    setup_fn: SweBenchHardSetupFn | None = chz.field(
        default=None,
        doc="You can pass a path to a function or a `chz` factory (e.g a class) that implements the SetupFn protocol.",
    )
    caas_container_image: str = chz.field(default="actions-runner-terminal-server")
    apply_code_patch: bool = chz.field(default=False)

    @tenacity.retry(
        retry=tenacity.retry_if_exception_type(
            (
                caas.ClientError,
                caas.ServerError,
                caas.ExecError,
                caas.TransportError,
                RuntimeError,
                httpx.ReadTimeout,
            )
        ),
        wait=tenacity.wait_random_exponential(min=120, max=600, multiplier=100),
        stop=tenacity.stop_after_attempt(3),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
    )
    async def initialize_resource(
        self, dp: datapoint.HarmonyDatapoint, volume_mounts: list[VolumeMount] | None = None
    ) -> CaasContainer:
        instance_id = dp.metadata.get("instance_id", None)
        version = dp.metadata.get("version", "sbh-12878")
        log_with_timestamp(f"Metadata keys: {dp.metadata.keys()}", instance_id)
        git_volume_mount_path = None
        if version.endswith("-noexec"):
            repo = dp.metadata.get("repo")
            commit = dp.metadata.get("base_commit")
            # Mount the git repo as a volume
            git_volume_mount_path = (
                f"/mnt/azure_blob/damajercak/swe/upload06272025/sbhv2/train/{repo}/{commit}.tar"
            )
            volume_mounts = (volume_mounts or []) + [
                VolumeMount(
                    host=git_volume_mount_path,
                    container=git_volume_mount_path,
                    deprecated_use_blobfuse=True,
                )
            ]
            log_with_timestamp(
                f"Mounting git volume for {repo}@{commit}, {volume_mounts=}", instance_id
            )
        else:
            log_with_timestamp(
                f"Not mounting git volume, version={version}, {volume_mounts=}", instance_id
            )

        async def custom_setup_fn(
            *,
            datapoint: dict[str, Any],
            terminal_session: TerminalSession,
        ):
            try:
                log_with_timestamp(f"Starting custom_setup_fn ...", instance_id)
                # determine whether to apply full setup or only clone
                do_setup = version.endswith("-noexec")
                run_test_script = None if do_setup else datapoint["metadata"].get("test_script")
                log_with_timestamp(
                    f"Repo {datapoint['metadata'].get('repo')}@{datapoint['metadata'].get('base_commit')} (do_setup={do_setup})",
                    instance_id,
                )

                # perform core setup (clone, initial diff, custom script)
                handle = TerminalSessionHandle()
                handle._ts_handle = terminal_session

                # Install prerequisite for the summarize_tests_tool
                await handle.exec(
                    cmd=["pip", "install", "junitparser"], timeout=1_800_000, workdir="/"
                )

                await initialize_resource(
                    handle=handle,
                    repo=datapoint["metadata"].get("repo"),
                    commit=datapoint["metadata"].get("base_commit"),
                    workdir=self.workdir,
                    instance_id=instance_id,
                    initial_gitdiff=datapoint["metadata"].get("initial_gitdiff", None),
                    setup_script=datapoint["metadata"].get("setup_script", None),
                    timeout=900_000,  # 15 minutes
                    do_setup=do_setup,
                    git_volume_mount_path=git_volume_mount_path,
                )
                # optional test tools only for full-exec instances
                if not do_setup:
                    # upload summarize_tests tool
                    with open(
                        "/root/code/glass/project/prbot_msft/prbot_msft/summarize_tests_tool.py",
                        "r",
                    ) as f:
                        await terminal_session.session.run(
                            UploadFile("/usr/local/bin/summarize_tests", f.read().encode())
                        )
                    await terminal_session.session.run(
                        RawExec(["chmod", "+x", "/usr/local/bin/summarize_tests"])
                    )
                    # upload expected outcomes
                    await terminal_session.session.run(
                        UploadFile(
                            "/test_filter.json",
                            json.dumps(
                                {
                                    "tests_results": datapoint["metadata"].get("tests_results"),
                                    "tests_results_patched": datapoint["metadata"].get(
                                        "tests_results_patched"
                                    ),
                                    "fail_to_pass": datapoint["metadata"].get("fail_to_pass"),
                                    "pass_to_pass": datapoint["metadata"].get("pass_to_pass"),
                                }
                            ).encode(),
                        )
                    )

                # Modify the run
                if run_test_script is not None:
                    run_test_script += "\nsummarize_tests"
                    await terminal_session.session.run(
                        UploadFile(
                            f"/{self.workdir}/{self.run_test_script}", run_test_script.encode()
                        )
                    )
                    await terminal_session.session.run(
                        RawExec(["chmod", "+x", f"/{self.workdir}/{self.run_test_script}"])
                    )
                    result = await terminal_session.session.run(
                        DownloadFileFromContainer(f"/{self.workdir}/{self.run_test_script}")
                    )
                    log_with_timestamp(
                        f"{self.run_test_script}:\n{result.decode('utf-8')}", instance_id
                    )

                if self.setup_fn is not None:
                    log_with_timestamp(f"Found setup_fn...", instance_id)
                    result = await self.setup_fn(
                        datapoint=datapoint,
                        session=terminal_session.session,
                        workdir=self.workdir,
                    )
                    log_with_timestamp(f"setup_fn done...", instance_id)

                if self.apply_code_patch:
                    patch = datapoint.get("metadata", {}).get("patch", "")
                    if patch:
                        log_with_timestamp(f"Uploading code patch...", instance_id)
                        # Upload patch
                        await terminal_session.session.run(
                            UploadFile("/code_patch", patch.encode())
                        )
                        # Apply patch
                        log_with_timestamp(f"Applying code patch...", instance_id)
                        await terminal_session.session.run(
                            RawExec(
                                ["git", "apply", "--allow-empty", "--reject", "/code_patch"],
                            )
                        )
                        log_with_timestamp(f"Code patch applied.", instance_id)
                    else:
                        log_with_timestamp(f"No code patch to apply.", instance_id)
                log_with_timestamp(f"custom_setup_fn done...", instance_id)
            except Exception as e:
                import traceback

                log_with_timestamp(f"Exception(custom_setup_fn):\n{repr(e)}", instance_id)
                log_with_timestamp(
                    f"Traceback(custom_setup_fn):\n{traceback.format_exc()}", instance_id
                )
                raise e
            return result

        azure_sas_token.assert_sas_token_properly_configured(
            caas_blobstore_name=self.caas_blobstore_container_name
        )
        if self.caas_session_state is not None:
            # We are not the owner of the container, so we don't need to run the setup function.
            resource = await self._initialize_resource_from_state(dp, self.caas_session_state)
            assert not resource.is_owner
            return resource

        caas_container_image = self.caas_container_image
        if dp.metadata.get("image_name", None) is None:
            log_with_timestamp(
                f"Sample does not specify image_name, using {caas_container_image}. Metadata keys: {dp.metadata.keys()}",
                instance_id,
            )
        else:
            log_with_timestamp(
                f"Sample specifies image_name: {dp.metadata.get('image_name', None)}", instance_id
            )
            caas_container_image = dp.metadata.get("image_name")

        try:
            log_with_timestamp(f"Starting container setup...", instance_id)
            async with leaky_bucket(rate_limit=self.rate_limit, max_occupancy=self.max_occupancy):
                caas_session_state = await _get_new_terminal_session_and_run_setup(
                    datapoint=dataclasses.asdict(dp),
                    caas_endpoint=self.caas_endpoint,
                    caas_container_image=caas_container_image,
                    setup_fn=custom_setup_fn,
                    caas_cpu_limit=self.caas_cpu_limit,
                    caas_memory_limit=self.caas_memory_limit,
                    caas_idle_ttl=self.caas_idle_ttl,
                    enable_network_after_setup=self.enable_network_after_setup,
                    caas_num_gpus=self.caas_num_gpus,
                    caas_network=self.caas_network,
                    env=dict(self.caas_env) if self.caas_env else None,
                    cmd=list(self.caas_new_session_cmd)
                    if self.caas_new_session_cmd is not None
                    else None,
                    volume_mounts=volume_mounts,
                    sandbox_runtime=self.caas_sandbox_runtime,
                )
            resource = CaasContainer(
                caas_endpoint=self.caas_endpoint,
                image_name=self.caas_container_image,
                caas_session_state=caas_session_state,
                user=self.user,
            )
            log_with_timestamp(f"Container setup done...", instance_id)
            assert resource.is_owner
            return resource
        except Exception as e:
            import traceback

            log_with_timestamp(f"Exception:\n{repr(e)}", instance_id)
            log_with_timestamp(f"Traceback:\n{traceback.format_exc()}", instance_id)


@chz.chz()
class SweBenchHardVSCCaasContainerResourceConfig(SweBenchHardCaasContainerResourceConfig):
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        return await super().initialize_resource(dp, volume_mounts=VSC_MNT)


class SWEBenchHardCaasContainerTool(
    DeepSWECaasContainerTool,
    OriginalBerryCaasContainerTool,
):
    """
    This leverages DeepSWECaasContainerTool, but also exposes default_exec_timeout.
    """

    def __init__(
        self,
        container: CaasContainer,
        terminal_emulator_path: str,
        exec_output_processor_path: str | None = None,
        max_execution_time: int = 3_600_000,
        tool_penalty_multiplier: float = 1.0,
        max_tool_penalty: float | None = None,
        default_exec_timeout: int | None = 1_800_000,
    ) -> None:
        OriginalBerryCaasContainerTool.__init__(
            self,
            container=container,
            terminal_emulator_path=terminal_emulator_path,
            exec_output_processor_path=exec_output_processor_path,
            max_execution_time=max_execution_time,
            default_exec_timeout=default_exec_timeout,
        )
        self.tool_penalty_multiplier = tool_penalty_multiplier
        self.max_tool_penalty = max_tool_penalty


@chz.chz(typecheck=True)
class SWEBenchHardContainerToolConfig(
    DeepSWECaasContainerToolConfig, OriginalCaasContainerToolConfig
):
    """
    This leverages DeepSWECaasContainerToolConfig, but exposes default_exec_timeout
    """

    tool_timeout: int = 1_980
    default_exec_timeout: int | None = 1_800_000

    def instruction(self, datapoint: HarmonyDatapoint | None = None) -> str:
        tool = SWEBenchHardCaasContainerTool(
            container=CaasContainer(
                caas_endpoint="UNUSED",
                image_name=self.caas_container_image,
                caas_session_state="UNUSED",
            ),
            terminal_emulator_path=self.terminal_emulator_path,
            default_exec_timeout=self.default_exec_timeout,
        )
        return tool.instruction()

    def _initialize_tool(
        self,
        **kwargs: Any,
    ) -> BerryTool:
        caas_container = kwargs[self._caas_resource_name]
        tool = SWEBenchHardCaasContainerTool(
            container=caas_container,
            terminal_emulator_path=self.terminal_emulator_path,
            exec_output_processor_path=self.exec_output_processor_path,
            max_execution_time=self.max_execution_time,
            tool_penalty_multiplier=self.tool_penalty_multiplier,
            max_tool_penalty=self.max_tool_penalty,
            default_exec_timeout=self.default_exec_timeout,
        )
        tool.plugins = create_caas_tool_plugins(tool, self.plugin_configs)
        return tool
