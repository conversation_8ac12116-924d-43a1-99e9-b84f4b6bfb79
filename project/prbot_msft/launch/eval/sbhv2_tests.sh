datetime=$(date +"%Y%m%d-%H%M")
# CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-mix13-pdw2-ev3-ivt-mixed-32x32-o4mini-tef03-5-tpm1-rm-lr1e-5-run8/policy/step_000160/"
CKPT="az://orngscuscresco/twapi/mini/e/damajercak-sbhv2-repair-w-test-task-20250720-2213/policy/"
INIT_CKPT="az://orngscuscresco/twapi/mini/e/zhendongwang-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run7/policy/step_000480/250713101112Y7TNMZOQ-0/" #"az://orngscuscresco/twapi/mini/e/damajercak-sbhv2-test-task-20250717-0741/policy"
CMD=(
beam python --use-cwd -m qstar.run_eval
nostrict
name=sbhv2-repair-test-metric-$datetime-peaval
# experiment_id=jadhuang-mix16-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-run2
auto_inherit_training_args=False 
eval_settings.checkpoint_dir="$CKPT"
policy.initial_checkpoint="$INIT_CKPT"
load.restore_from_all_clusters=False

# :berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
# root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
# policy.ml_config='ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
# peashooter.sampling_use_ev3=True

:berry_models.scallion_lpe:d36_80g_mbg16_bf16
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=True'  
policy.ml_config='ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false'  
policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True ixf_sampling_extension_gpu_to_cpu_async=True'

policy.is_multimodal=True
...encoding_name=orion_200k  
...harmony_renderer_name=harmony_v4.0.15_berry_v3_1mil_orion_lpe

peashooter.timeout_seconds.stalled_datapoint=3600
peashooter.timeout_seconds.lost_datapoint=1800
peashooter.use_stale_kv_cache=False
load.restore_from_all_clusters=False
max_workers_per_step=5

policy.n_ctx=262144
defaults.n_ctx=262144
...max_num_yields=256
defaults.sampler.max_num_yields=256
policy.encoding_name=orion_200k
defaults.sampler.harmony_constrained_sampling=True
eval_settings.eval_initial_policy=True
eval_settings.eval_every=30

:prbot_msft.presets.swebench_hard:swebenchhard_repair_06202025_sbhv2_testval_padawan_silver
# :prbot_msft.presets.swebench_hard:swebenchhard_12878_test_hq_padawan_or_testval
:prbot_msft.presets.swebench_hard:o3_hpe_cotograder_bus
...dataset_container=orngscuscresco

security_profile=msft-orng
github_upload=False
wandb_enable=True
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a peaval-$datetime.log